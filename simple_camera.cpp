#include <iostream>
#include <windows.h>
#include <vfw.h>
#include <string>
#include <ctime>
#include <vector>
#include <algorithm>

// Use ANSI versions for compatibility
#undef UNICODE
#undef _UNICODE

// Add debug output
#define DEBUG_OUTPUT 1

class SimpleCameraApp {
private:
    HWND hwndCap;
    HWND hwndMain;
    bool isCapturing;
    int currentCameraIndex;
    std::vector<int> availableCameras;

public:
    SimpleCameraApp() : hwndCap(nullptr), hwndMain(nullptr), isCapturing(false), currentCameraIndex(0) {}
    
    bool initialize() {
        // Scan for available cameras first
        scanAvailableCameras();

        // Register window class
        WNDCLASS wc = {};
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = GetModuleHandle(nullptr);
        wc.lpszClassName = "SimpleCameraApp";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);

        if (!RegisterClass(&wc)) {
            std::cout << "Failed to register window class" << std::endl;
            return false;
        }

        // Create main window
        hwndMain = CreateWindowEx(
            0,
            "SimpleCameraApp",
            "Professional Camera - Enhanced Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT, 900, 700,
            nullptr, nullptr, GetModuleHandle(nullptr), this
        );

        if (!hwndMain) {
            std::cout << "Failed to create main window" << std::endl;
            return false;
        }
        
        // Create capture window with better parameters
        hwndCap = capCreateCaptureWindow(
            "Camera Preview",
            WS_CHILD | WS_VISIBLE | WS_BORDER,
            10, 80, 720, 540,
            hwndMain, 1
        );

        if (!hwndCap) {
            std::cout << "Failed to create capture window" << std::endl;
            return false;
        }

        // Try to connect to the best available camera
        bool connected = false;
        for (int camIndex : availableCameras) {
            std::cout << "Trying to connect to camera " << camIndex << std::endl;
            if (capDriverConnect(hwndCap, camIndex)) {
                std::cout << "Successfully connected to camera " << camIndex << std::endl;
                currentCameraIndex = camIndex;
                connected = true;
                break;
            }
        }

        if (!connected) {
            std::cout << "Failed to connect to any camera driver" << std::endl;
            // Don't return false - let user try manual connection
        }

        // Set up enhanced capture parameters
        CAPTUREPARMS capParms;
        capCaptureGetSetup(hwndCap, &capParms, sizeof(capParms));
        capParms.dwRequestMicroSecPerFrame = 33333; // ~30 FPS
        capParms.fMakeUserHitOKToCapture = FALSE;
        capParms.fYield = TRUE;
        capParms.fCaptureAudio = FALSE;
        capParms.fAbortLeftMouse = FALSE;
        capParms.fAbortRightMouse = FALSE;
        capParms.fLimitEnabled = FALSE;
        capParms.wPercentDropForError = 10;
        capCaptureSetSetup(hwndCap, &capParms, sizeof(capParms));
        
        // Create enhanced control buttons
        CreateWindow("BUTTON", "Start Camera",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
            750, 80, 120, 35, hwndMain, (HMENU)2, GetModuleHandle(nullptr), nullptr);

        CreateWindow("BUTTON", "Stop Camera",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            750, 125, 120, 35, hwndMain, (HMENU)3, GetModuleHandle(nullptr), nullptr);

        CreateWindow("BUTTON", "Capture Photo",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            750, 170, 120, 35, hwndMain, (HMENU)4, GetModuleHandle(nullptr), nullptr);

        CreateWindow("BUTTON", "Try Next Camera",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            750, 215, 120, 35, hwndMain, (HMENU)6, GetModuleHandle(nullptr), nullptr);

        CreateWindow("BUTTON", "Camera Settings",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            750, 260, 120, 35, hwndMain, (HMENU)5, GetModuleHandle(nullptr), nullptr);

        // Add enhanced status text
        std::string statusText = "Professional Camera v1.1\nEnhanced Edition\n\nAvailable Cameras: " +
                                std::to_string(availableCameras.size()) +
                                "\nCurrent: Camera " + std::to_string(currentCameraIndex) +
                                "\n\nFeatures:\n- Enhanced camera detection\n- Multiple camera support\n- Improved photo capture\n- Better error handling";

        CreateWindow("STATIC", statusText.c_str(),
            WS_VISIBLE | WS_CHILD,
            750, 320, 140, 280, hwndMain, nullptr, GetModuleHandle(nullptr), nullptr);
        
        ShowWindow(hwndMain, SW_SHOW);
        UpdateWindow(hwndMain);
        
        return true;
    }

    void scanAvailableCameras() {
        availableCameras.clear();
        std::cout << "Scanning for available cameras..." << std::endl;

        // Test cameras 0-9 (most systems have cameras in this range)
        for (int i = 0; i < 10; i++) {
            char driverName[256];
            char driverVersion[256];

            if (capGetDriverDescription(i, driverName, sizeof(driverName),
                                      driverVersion, sizeof(driverVersion))) {
                std::cout << "Found camera " << i << ": " << driverName << " (" << driverVersion << ")" << std::endl;
                availableCameras.push_back(i);
            }
        }

        if (availableCameras.empty()) {
            std::cout << "No cameras found via driver enumeration, will try direct connection" << std::endl;
            // Add default indices to try
            availableCameras.push_back(0);
            availableCameras.push_back(1);
        }
    }

    void tryNextCamera() {
        if (availableCameras.empty()) {
            MessageBox(hwndMain, "No cameras available to try", "No Cameras", MB_OK | MB_ICONWARNING);
            return;
        }

        // Stop current capture
        if (isCapturing) {
            stopCapture();
        }

        // Disconnect current camera
        if (hwndCap) {
            capDriverDisconnect(hwndCap);
        }

        // Find next camera index
        auto it = std::find(availableCameras.begin(), availableCameras.end(), currentCameraIndex);
        if (it != availableCameras.end()) {
            ++it;
            if (it == availableCameras.end()) {
                it = availableCameras.begin();
            }
            currentCameraIndex = *it;
        } else {
            currentCameraIndex = availableCameras[0];
        }

        // Try to connect to new camera
        std::cout << "Trying to connect to camera " << currentCameraIndex << std::endl;
        if (capDriverConnect(hwndCap, currentCameraIndex)) {
            std::cout << "Successfully connected to camera " << currentCameraIndex << std::endl;
            MessageBox(hwndMain, ("Connected to Camera " + std::to_string(currentCameraIndex)).c_str(),
                      "Camera Connected", MB_OK | MB_ICONINFORMATION);
        } else {
            std::cout << "Failed to connect to camera " << currentCameraIndex << std::endl;
            MessageBox(hwndMain, ("Failed to connect to Camera " + std::to_string(currentCameraIndex)).c_str(),
                      "Connection Failed", MB_OK | MB_ICONERROR);
        }
    }

    void startCapture() {
        if (!isCapturing) {
            capPreview(hwndCap, TRUE);
            capPreviewScale(hwndCap, TRUE);
            capPreviewRate(hwndCap, 33); // 30 FPS
            isCapturing = true;
            std::cout << "Camera started" << std::endl;
        }
    }
    
    void stopCapture() {
        if (isCapturing) {
            capPreview(hwndCap, FALSE);
            isCapturing = false;
            std::cout << "Camera stopped" << std::endl;
        }
    }
    
    void capturePhoto() {
        std::cout << "Capture photo requested..." << std::endl;

        if (!hwndCap) {
            MessageBox(hwndMain, "Camera window not available", "Error", MB_OK | MB_ICONERROR);
            return;
        }

        // Generate filename with timestamp
        time_t now = time(0);
        struct tm timeinfo;
        localtime_s(&timeinfo, &now);

        char filename[256];
        sprintf_s(filename, "professional_camera_%04d%02d%02d_%02d%02d%02d.bmp",
            timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
            timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec);

        std::cout << "Attempting to save photo as: " << filename << std::endl;

        // Try multiple capture methods
        bool success = false;

        // Method 1: Direct file save
        if (capFileSaveDIB(hwndCap, filename)) {
            success = true;
            std::cout << "Photo saved using capFileSaveDIB: " << filename << std::endl;
        }
        // Method 2: Single frame capture
        else if (capGrabFrameNoStop(hwndCap) && capFileSaveDIB(hwndCap, filename)) {
            success = true;
            std::cout << "Photo saved using capGrabFrameNoStop: " << filename << std::endl;
        }
        // Method 3: Try with different filename
        else {
            sprintf_s(filename, "camera_capture_%d.bmp", (int)now);
            if (capFileSaveDIB(hwndCap, filename)) {
                success = true;
                std::cout << "Photo saved with simplified name: " << filename << std::endl;
            }
        }

        if (success) {
            std::string message = "Photo saved successfully!\n\nFilename: ";
            message += filename;
            message += "\n\nLocation: Current directory";
            MessageBox(hwndMain, message.c_str(), "Photo Saved!", MB_OK | MB_ICONINFORMATION);
        } else {
            std::cout << "All capture methods failed" << std::endl;
            std::string errorMsg = "Failed to save photo.\n\nPossible causes:\n";
            errorMsg += "- Camera not properly connected\n";
            errorMsg += "- No camera preview active\n";
            errorMsg += "- Insufficient disk space\n";
            errorMsg += "- File permissions issue\n\n";
            errorMsg += "Try:\n1. Start camera preview first\n2. Try 'Try Next Camera' button\n3. Check camera settings";
            MessageBox(hwndMain, errorMsg.c_str(), "Capture Failed", MB_OK | MB_ICONERROR);
        }
    }
    
    void showCameraSettings() {
        if (hwndCap) {
            capDlgVideoFormat(hwndCap);
        }
    }
    
    void run() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }
    
    void cleanup() {
        if (hwndCap) {
            capDriverDisconnect(hwndCap);
        }
    }
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        SimpleCameraApp* app = nullptr;
        
        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
            app = reinterpret_cast<SimpleCameraApp*>(pCreate->lpCreateParams);
            SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(app));
        } else {
            app = reinterpret_cast<SimpleCameraApp*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
        }
        
        if (app) {
            switch (uMsg) {
                case WM_COMMAND:
                    switch (LOWORD(wParam)) {
                        case 2: // Start Camera
                            app->startCapture();
                            break;
                        case 3: // Stop Camera
                            app->stopCapture();
                            break;
                        case 4: // Capture Photo
                            app->capturePhoto();
                            break;
                        case 5: // Camera Settings
                            app->showCameraSettings();
                            break;
                        case 6: // Try Next Camera
                            app->tryNextCamera();
                            break;
                    }
                    break;
                    
                case WM_DESTROY:
                    app->cleanup();
                    PostQuitMessage(0);
                    break;
            }
        }
        
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
};

int main() {
    std::cout << "Professional Camera Application - Simple Edition" << std::endl;
    std::cout << "Initializing camera system..." << std::endl;

    SimpleCameraApp app;

    if (!app.initialize()) {
        std::cout << "Failed to initialize camera application" << std::endl;
        MessageBox(nullptr, "Failed to initialize camera application.\nPlease check if a camera is connected.", "Initialization Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    std::cout << "Camera application started successfully!" << std::endl;
    std::cout << "Click 'Start Camera' to begin preview" << std::endl;

    app.run();

    return 0;
}
