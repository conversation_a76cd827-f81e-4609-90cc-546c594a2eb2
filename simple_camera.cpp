#include <iostream>
#include <windows.h>
#include <vfw.h>
#include <string>
#include <ctime>

// Use ANSI versions for compatibility
#undef UNICODE
#undef _UNICODE

class SimpleCameraApp {
private:
    HWND hwndCap;
    HWND hwndMain;
    bool isCapturing;
    
public:
    SimpleCameraApp() : hwndCap(nullptr), hwndMain(nullptr), isCapturing(false) {}
    
    bool initialize() {
        // Register window class
        WNDCLASS wc = {};
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = GetModuleHandle(nullptr);
        wc.lpszClassName = "SimpleCameraApp";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);

        if (!RegisterClass(&wc)) {
            return false;
        }

        // Create main window
        hwndMain = CreateWindowEx(
            0,
            "SimpleCameraApp",
            "Professional Camera - Simple Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
            nullptr, nullptr, GetModuleHandle(nullptr), this
        );
        
        if (!hwndMain) {
            return false;
        }
        
        // Create capture window
        hwndCap = capCreateCaptureWindow(
            "Camera Preview",
            WS_CHILD | WS_VISIBLE,
            10, 50, 640, 480,
            hwndMain, 1
        );

        if (!hwndCap) {
            std::cout << "Failed to create capture window" << std::endl;
            return false;
        }

        // Connect to camera driver
        if (!capDriverConnect(hwndCap, 0)) {
            std::cout << "Failed to connect to camera driver" << std::endl;
            return false;
        }

        // Set up capture parameters
        CAPTUREPARMS capParms;
        capCaptureGetSetup(hwndCap, &capParms, sizeof(capParms));
        capParms.dwRequestMicroSecPerFrame = 33333; // ~30 FPS
        capParms.fMakeUserHitOKToCapture = FALSE;
        capParms.fYield = TRUE;
        capParms.fCaptureAudio = FALSE;
        capCaptureSetSetup(hwndCap, &capParms, sizeof(capParms));
        
        // Create control buttons
        CreateWindow("BUTTON", "Start Camera",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
            670, 50, 100, 30, hwndMain, (HMENU)2, GetModuleHandle(nullptr), nullptr);

        CreateWindow("BUTTON", "Stop Camera",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            670, 90, 100, 30, hwndMain, (HMENU)3, GetModuleHandle(nullptr), nullptr);

        CreateWindow("BUTTON", "Capture Photo",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            670, 130, 100, 30, hwndMain, (HMENU)4, GetModuleHandle(nullptr), nullptr);

        CreateWindow("BUTTON", "Camera Settings",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            670, 170, 100, 30, hwndMain, (HMENU)5, GetModuleHandle(nullptr), nullptr);

        // Add status text
        CreateWindow("STATIC", "Professional Camera v1.0\nSimple Edition\n\nFeatures:\n- Live camera preview\n- Photo capture\n- Camera settings\n- Professional interface",
            WS_VISIBLE | WS_CHILD,
            670, 220, 120, 200, hwndMain, nullptr, GetModuleHandle(nullptr), nullptr);
        
        ShowWindow(hwndMain, SW_SHOW);
        UpdateWindow(hwndMain);
        
        return true;
    }
    
    void startCapture() {
        if (!isCapturing) {
            capPreview(hwndCap, TRUE);
            capPreviewScale(hwndCap, TRUE);
            capPreviewRate(hwndCap, 33); // 30 FPS
            isCapturing = true;
            std::cout << "Camera started" << std::endl;
        }
    }
    
    void stopCapture() {
        if (isCapturing) {
            capPreview(hwndCap, FALSE);
            isCapturing = false;
            std::cout << "Camera stopped" << std::endl;
        }
    }
    
    void capturePhoto() {
        if (isCapturing) {
            // Generate filename with timestamp
            time_t now = time(0);
            struct tm timeinfo;
            localtime_s(&timeinfo, &now);

            char filename[256];
            sprintf_s(filename, "professional_camera_%04d%02d%02d_%02d%02d%02d.bmp",
                timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
                timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec);

            if (capFileSaveDIB(hwndCap, filename)) {
                std::cout << "Photo saved: " << filename << std::endl;
                MessageBox(hwndMain, filename, "Photo Saved Successfully!", MB_OK | MB_ICONINFORMATION);
            } else {
                MessageBox(hwndMain, "Failed to save photo", "Error", MB_OK | MB_ICONERROR);
            }
        } else {
            MessageBox(hwndMain, "Please start the camera first", "Camera Not Active", MB_OK | MB_ICONWARNING);
        }
    }
    
    void showCameraSettings() {
        if (hwndCap) {
            capDlgVideoFormat(hwndCap);
        }
    }
    
    void run() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }
    
    void cleanup() {
        if (hwndCap) {
            capDriverDisconnect(hwndCap);
        }
    }
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        SimpleCameraApp* app = nullptr;
        
        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
            app = reinterpret_cast<SimpleCameraApp*>(pCreate->lpCreateParams);
            SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(app));
        } else {
            app = reinterpret_cast<SimpleCameraApp*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
        }
        
        if (app) {
            switch (uMsg) {
                case WM_COMMAND:
                    switch (LOWORD(wParam)) {
                        case 2: // Start Camera
                            app->startCapture();
                            break;
                        case 3: // Stop Camera
                            app->stopCapture();
                            break;
                        case 4: // Capture Photo
                            app->capturePhoto();
                            break;
                        case 5: // Camera Settings
                            app->showCameraSettings();
                            break;
                    }
                    break;
                    
                case WM_DESTROY:
                    app->cleanup();
                    PostQuitMessage(0);
                    break;
            }
        }
        
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
};

int main() {
    std::cout << "Professional Camera Application - Simple Edition" << std::endl;
    std::cout << "Initializing camera system..." << std::endl;

    SimpleCameraApp app;

    if (!app.initialize()) {
        std::cout << "Failed to initialize camera application" << std::endl;
        MessageBox(nullptr, "Failed to initialize camera application.\nPlease check if a camera is connected.", "Initialization Error", MB_OK | MB_ICONERROR);
        return -1;
    }

    std::cout << "Camera application started successfully!" << std::endl;
    std::cout << "Click 'Start Camera' to begin preview" << std::endl;

    app.run();

    return 0;
}
