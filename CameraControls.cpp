#include "CameraControls.h"
#include <QApplication>

CameraControls::CameraControls(QWidget *parent)
    : QWidget(parent)
    , m_cameraCapture(nullptr)
    , m_mainLayout(nullptr)
    , m_tabWidget(nullptr)
{
    setupUI();
    connectSignals();
    updateFromSettings();
}

void CameraControls::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_tabWidget = new QTabWidget();
    
    setupCameraTab();
    setupEnhancementTab();
    setupPresetsTab();
    
    m_mainLayout->addWidget(m_tabWidget);
    
    // Action buttons
    m_buttonLayout = new QHBoxLayout();
    m_captureBtn = new QPushButton("Capture Photo");
    m_captureBtn->setStyleSheet("QPushButton { background-color: #4CAF50; font-weight: bold; padding: 12px; }");
    m_resetDefaultsBtn = new QPushButton("Reset to Defaults");
    
    m_buttonLayout->addWidget(m_captureBtn);
    m_buttonLayout->addStretch();
    m_buttonLayout->addWidget(m_resetDefaultsBtn);
    
    m_mainLayout->addLayout(m_buttonLayout);
}

void CameraControls::setupCameraTab()
{
    m_cameraTab = new QWidget();
    QVBoxLayout* cameraLayout = new QVBoxLayout(m_cameraTab);
    
    // Exposure Group
    m_exposureGroup = new QGroupBox("Exposure");
    QGridLayout* exposureLayout = new QGridLayout(m_exposureGroup);
    
    m_autoExposureCheck = new QCheckBox("Auto Exposure");
    m_autoExposureCheck->setChecked(true);
    exposureLayout->addWidget(m_autoExposureCheck, 0, 0, 1, 3);
    
    m_exposureSlider = createSlider(-300, 300, 0, 50);
    m_exposureLabel = createValueLabel("0.0 EV");
    addControlRow(exposureLayout, 1, "Exposure:", m_exposureSlider, m_exposureLabel);
    
    m_autoISOCheck = new QCheckBox("Auto ISO");
    m_autoISOCheck->setChecked(true);
    exposureLayout->addWidget(m_autoISOCheck, 2, 0, 1, 3);
    
    m_isoSpin = new QSpinBox();
    m_isoSpin->setRange(100, 6400);
    m_isoSpin->setValue(100);
    m_isoSpin->setSingleStep(100);
    addControlRow(exposureLayout, 3, "ISO:", m_isoSpin);
    
    // Color Group
    m_colorGroup = new QGroupBox("Color");
    QGridLayout* colorLayout = new QGridLayout(m_colorGroup);
    
    m_autoWBCheck = new QCheckBox("Auto White Balance");
    m_autoWBCheck->setChecked(true);
    colorLayout->addWidget(m_autoWBCheck, 0, 0, 1, 3);
    
    m_whiteBalanceSpin = new QSpinBox();
    m_whiteBalanceSpin->setRange(2000, 10000);
    m_whiteBalanceSpin->setValue(5500);
    m_whiteBalanceSpin->setSingleStep(100);
    m_whiteBalanceSpin->setSuffix(" K");
    addControlRow(colorLayout, 1, "White Balance:", m_whiteBalanceSpin);
    
    // Focus Group
    m_focusGroup = new QGroupBox("Focus");
    QGridLayout* focusLayout = new QGridLayout(m_focusGroup);
    
    m_autoFocusCheck = new QCheckBox("Auto Focus");
    m_autoFocusCheck->setChecked(true);
    focusLayout->addWidget(m_autoFocusCheck, 0, 0, 1, 3);
    
    m_focusSlider = createSlider(0, 100, 50, 10);
    m_focusLabel = createValueLabel("50%");
    addControlRow(focusLayout, 1, "Focus:", m_focusSlider, m_focusLabel);
    
    // Quality Group
    m_qualityGroup = new QGroupBox("Quality");
    QGridLayout* qualityLayout = new QGridLayout(m_qualityGroup);
    
    m_resolutionCombo = new QComboBox();
    m_resolutionCombo->addItems({"720p", "1080p", "1440p", "4K"});
    m_resolutionCombo->setCurrentText("1080p");
    addControlRow(qualityLayout, 0, "Resolution:", m_resolutionCombo);
    
    m_frameRateCombo = new QComboBox();
    m_frameRateCombo->addItems({"15 fps", "24 fps", "30 fps", "60 fps"});
    m_frameRateCombo->setCurrentText("30 fps");
    addControlRow(qualityLayout, 1, "Frame Rate:", m_frameRateCombo);
    
    m_stabilizationCheck = new QCheckBox("Image Stabilization");
    m_stabilizationCheck->setChecked(true);
    qualityLayout->addWidget(m_stabilizationCheck, 2, 0, 1, 3);
    
    m_hdrCheck = new QCheckBox("HDR Mode");
    qualityLayout->addWidget(m_hdrCheck, 3, 0, 1, 3);
    
    cameraLayout->addWidget(m_exposureGroup);
    cameraLayout->addWidget(m_colorGroup);
    cameraLayout->addWidget(m_focusGroup);
    cameraLayout->addWidget(m_qualityGroup);
    cameraLayout->addStretch();
    
    m_tabWidget->addTab(m_cameraTab, "Camera");
}

void CameraControls::setupEnhancementTab()
{
    m_enhancementTab = new QWidget();
    QVBoxLayout* enhancementLayout = new QVBoxLayout(m_enhancementTab);
    
    // Noise Reduction Group
    m_noiseGroup = new QGroupBox("Noise Reduction");
    QGridLayout* noiseLayout = new QGridLayout(m_noiseGroup);
    
    m_noiseReductionSlider = createSlider(0, 100, 50, 10);
    m_noiseReductionLabel = createValueLabel("50%");
    addControlRow(noiseLayout, 0, "Strength:", m_noiseReductionSlider, m_noiseReductionLabel);
    
    // Sharpening Group
    m_sharpenGroup = new QGroupBox("Sharpening");
    QGridLayout* sharpenLayout = new QGridLayout(m_sharpenGroup);
    
    m_sharpeningSlider = createSlider(0, 100, 30, 10);
    m_sharpeningLabel = createValueLabel("30%");
    addControlRow(sharpenLayout, 0, "Amount:", m_sharpeningSlider, m_sharpeningLabel);
    
    m_unsharpAmountSpin = new QDoubleSpinBox();
    m_unsharpAmountSpin->setRange(0.0, 5.0);
    m_unsharpAmountSpin->setValue(1.5);
    m_unsharpAmountSpin->setSingleStep(0.1);
    addControlRow(sharpenLayout, 1, "Unsharp Amount:", m_unsharpAmountSpin);
    
    m_unsharpRadiusSpin = new QDoubleSpinBox();
    m_unsharpRadiusSpin->setRange(0.1, 5.0);
    m_unsharpRadiusSpin->setValue(1.0);
    m_unsharpRadiusSpin->setSingleStep(0.1);
    addControlRow(sharpenLayout, 2, "Unsharp Radius:", m_unsharpRadiusSpin);
    
    // Color Enhancement Group
    m_colorEnhanceGroup = new QGroupBox("Color Enhancement");
    QGridLayout* colorEnhanceLayout = new QGridLayout(m_colorEnhanceGroup);
    
    m_saturationSlider = createSlider(0, 200, 120, 20);
    m_saturationLabel = createValueLabel("120%");
    addControlRow(colorEnhanceLayout, 0, "Saturation:", m_saturationSlider, m_saturationLabel);
    
    m_vibranceSlider = createSlider(-100, 100, 30, 20);
    m_vibranceLabel = createValueLabel("30%");
    addControlRow(colorEnhanceLayout, 1, "Vibrance:", m_vibranceSlider, m_vibranceLabel);
    
    m_contrastSlider = createSlider(50, 200, 110, 20);
    m_contrastLabel = createValueLabel("110%");
    addControlRow(colorEnhanceLayout, 2, "Contrast:", m_contrastSlider, m_contrastLabel);
    
    m_brightnessSlider = createSlider(-100, 100, 0, 20);
    m_brightnessLabel = createValueLabel("0%");
    addControlRow(colorEnhanceLayout, 3, "Brightness:", m_brightnessSlider, m_brightnessLabel);
    
    enhancementLayout->addWidget(m_noiseGroup);
    enhancementLayout->addWidget(m_sharpenGroup);
    enhancementLayout->addWidget(m_colorEnhanceGroup);
    
    m_tabWidget->addTab(m_enhancementTab, "Enhancement");
}

void CameraControls::setupPresetsTab()
{
    m_presetsTab = new QWidget();
    QVBoxLayout* presetsLayout = new QVBoxLayout(m_presetsTab);
    
    QGroupBox* presetsGroup = new QGroupBox("Presets");
    QGridLayout* presetsGridLayout = new QGridLayout(presetsGroup);
    
    m_presetCombo = new QComboBox();
    m_presetCombo->addItems({
        "Default",
        "Portrait",
        "Landscape",
        "Low Light",
        "High Contrast",
        "Vintage",
        "Professional"
    });
    
    m_loadPresetBtn = new QPushButton("Load Preset");
    m_savePresetBtn = new QPushButton("Save Preset");
    m_resetBtn = new QPushButton("Reset All");
    
    presetsGridLayout->addWidget(new QLabel("Preset:"), 0, 0);
    presetsGridLayout->addWidget(m_presetCombo, 0, 1);
    presetsGridLayout->addWidget(m_loadPresetBtn, 1, 0);
    presetsGridLayout->addWidget(m_savePresetBtn, 1, 1);
    presetsGridLayout->addWidget(m_resetBtn, 2, 0, 1, 2);
    
    presetsLayout->addWidget(presetsGroup);
    presetsLayout->addStretch();
    
    m_tabWidget->addTab(m_presetsTab, "Presets");
}

void CameraControls::connectSignals()
{
    // Camera controls
    connect(m_autoExposureCheck, &QCheckBox::toggled, this, &CameraControls::onCameraSettingChanged);
    connect(m_exposureSlider, &QSlider::valueChanged, this, [this](int value) {
        m_exposureLabel->setText(formatSliderValue(value, 0.01, " EV"));
        onCameraSettingChanged();
    });
    
    connect(m_autoISOCheck, &QCheckBox::toggled, this, &CameraControls::onCameraSettingChanged);
    connect(m_isoSpin, QOverload<int>::of(&QSpinBox::valueChanged), this, &CameraControls::onCameraSettingChanged);
    
    connect(m_autoWBCheck, &QCheckBox::toggled, this, &CameraControls::onCameraSettingChanged);
    connect(m_whiteBalanceSpin, QOverload<int>::of(&QSpinBox::valueChanged), this, &CameraControls::onCameraSettingChanged);
    
    connect(m_autoFocusCheck, &QCheckBox::toggled, this, &CameraControls::onCameraSettingChanged);
    connect(m_focusSlider, &QSlider::valueChanged, this, [this](int value) {
        m_focusLabel->setText(formatSliderValue(value, 1.0, "%"));
        onCameraSettingChanged();
    });
    
    connect(m_resolutionCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &CameraControls::onCameraSettingChanged);
    connect(m_frameRateCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &CameraControls::onCameraSettingChanged);
    connect(m_stabilizationCheck, &QCheckBox::toggled, this, &CameraControls::onCameraSettingChanged);
    connect(m_hdrCheck, &QCheckBox::toggled, this, &CameraControls::onCameraSettingChanged);
    
    // Enhancement controls
    connect(m_noiseReductionSlider, &QSlider::valueChanged, this, [this](int value) {
        m_noiseReductionLabel->setText(formatSliderValue(value, 1.0, "%"));
        onEnhancementSettingChanged();
    });
    
    connect(m_sharpeningSlider, &QSlider::valueChanged, this, [this](int value) {
        m_sharpeningLabel->setText(formatSliderValue(value, 1.0, "%"));
        onEnhancementSettingChanged();
    });
    
    connect(m_unsharpAmountSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &CameraControls::onEnhancementSettingChanged);
    connect(m_unsharpRadiusSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &CameraControls::onEnhancementSettingChanged);
    
    connect(m_saturationSlider, &QSlider::valueChanged, this, [this](int value) {
        m_saturationLabel->setText(formatSliderValue(value, 1.0, "%"));
        onEnhancementSettingChanged();
    });
    
    connect(m_vibranceSlider, &QSlider::valueChanged, this, [this](int value) {
        m_vibranceLabel->setText(formatSliderValue(value, 1.0, "%"));
        onEnhancementSettingChanged();
    });
    
    connect(m_contrastSlider, &QSlider::valueChanged, this, [this](int value) {
        m_contrastLabel->setText(formatSliderValue(value, 1.0, "%"));
        onEnhancementSettingChanged();
    });
    
    connect(m_brightnessSlider, &QSlider::valueChanged, this, [this](int value) {
        m_brightnessLabel->setText(formatSliderValue(value, 1.0, "%"));
        onEnhancementSettingChanged();
    });
    
    // Buttons
    connect(m_captureBtn, &QPushButton::clicked, this, &CameraControls::capturePhoto);
    connect(m_resetDefaultsBtn, &QPushButton::clicked, this, &CameraControls::onResetDefaults);
    connect(m_loadPresetBtn, &QPushButton::clicked, this, &CameraControls::onPresetSelected);
}

QSlider* CameraControls::createSlider(int min, int max, int value, int tickInterval)
{
    QSlider* slider = new QSlider(Qt::Horizontal);
    slider->setRange(min, max);
    slider->setValue(value);
    if (tickInterval > 0) {
        slider->setTickPosition(QSlider::TicksBelow);
        slider->setTickInterval(tickInterval);
    }
    return slider;
}

QLabel* CameraControls::createValueLabel(const QString& text)
{
    QLabel* label = new QLabel(text);
    label->setMinimumWidth(60);
    label->setAlignment(Qt::AlignCenter);
    label->setStyleSheet("QLabel { background-color: #404040; border: 1px solid #555; border-radius: 3px; padding: 2px; }");
    return label;
}

void CameraControls::addControlRow(QGridLayout* layout, int row, const QString& label, QWidget* control, QLabel* valueLabel)
{
    layout->addWidget(new QLabel(label), row, 0);
    layout->addWidget(control, row, 1);
    if (valueLabel) {
        layout->addWidget(valueLabel, row, 2);
    }
}

QString CameraControls::formatSliderValue(int value, double scale, const QString& suffix)
{
    return QString::number(value * scale, 'f', scale < 1.0 ? 1 : 0) + suffix;
}

void CameraControls::setCameraCapture(CameraCapture* capture)
{
    m_cameraCapture = capture;
}

void CameraControls::updateFromSettings()
{
    // This will be called to update UI from current settings
    updateCameraSettings();
    updateEnhancementSettings();
}

void CameraControls::onCameraSettingChanged()
{
    updateCameraSettings();
    emit cameraSettingsChanged(m_currentCameraSettings);
}

void CameraControls::onEnhancementSettingChanged()
{
    updateEnhancementSettings();
    emit enhancementSettingsChanged(m_currentEnhancementSettings);
}

void CameraControls::updateCameraSettings()
{
    m_currentCameraSettings.autoExposure = m_autoExposureCheck->isChecked();
    m_currentCameraSettings.exposure = m_exposureSlider->value() * 0.01;
    m_currentCameraSettings.autoISO = m_autoISOCheck->isChecked();
    m_currentCameraSettings.iso = m_isoSpin->value();
    m_currentCameraSettings.autoWhiteBalance = m_autoWBCheck->isChecked();
    m_currentCameraSettings.whiteBalance = m_whiteBalanceSpin->value();
    m_currentCameraSettings.autoFocus = m_autoFocusCheck->isChecked();
    m_currentCameraSettings.focus = m_focusSlider->value() * 0.01;
    
    // Resolution mapping
    QString resText = m_resolutionCombo->currentText();
    if (resText == "720p") m_currentCameraSettings.resolution = 720;
    else if (resText == "1080p") m_currentCameraSettings.resolution = 1080;
    else if (resText == "1440p") m_currentCameraSettings.resolution = 1440;
    else if (resText == "4K") m_currentCameraSettings.resolution = 2160;
    
    // Frame rate mapping
    QString fpsText = m_frameRateCombo->currentText();
    m_currentCameraSettings.frameRate = fpsText.split(" ").first().toInt();
    
    m_currentCameraSettings.stabilization = m_stabilizationCheck->isChecked();
    m_currentCameraSettings.hdr = m_hdrCheck->isChecked();
}

void CameraControls::updateEnhancementSettings()
{
    m_currentEnhancementSettings.noiseReduction = m_noiseReductionSlider->value() * 0.01;
    m_currentEnhancementSettings.sharpening = m_sharpeningSlider->value() * 0.01;
    m_currentEnhancementSettings.unsharpMaskAmount = m_unsharpAmountSpin->value();
    m_currentEnhancementSettings.unsharpMaskRadius = m_unsharpRadiusSpin->value();
    m_currentEnhancementSettings.saturation = m_saturationSlider->value() * 0.01;
    m_currentEnhancementSettings.vibrance = m_vibranceSlider->value() * 0.01;
    m_currentEnhancementSettings.contrast = m_contrastSlider->value() * 0.01;
    m_currentEnhancementSettings.brightness = m_brightnessSlider->value() * 0.01;
}

void CameraControls::onResetDefaults()
{
    // Reset all controls to default values
    m_autoExposureCheck->setChecked(true);
    m_exposureSlider->setValue(0);
    m_autoISOCheck->setChecked(true);
    m_isoSpin->setValue(100);
    m_autoWBCheck->setChecked(true);
    m_whiteBalanceSpin->setValue(5500);
    m_autoFocusCheck->setChecked(true);
    m_focusSlider->setValue(50);
    m_resolutionCombo->setCurrentText("1080p");
    m_frameRateCombo->setCurrentText("30 fps");
    m_stabilizationCheck->setChecked(true);
    m_hdrCheck->setChecked(false);
    
    m_noiseReductionSlider->setValue(50);
    m_sharpeningSlider->setValue(30);
    m_unsharpAmountSpin->setValue(1.5);
    m_unsharpRadiusSpin->setValue(1.0);
    m_saturationSlider->setValue(120);
    m_vibranceSlider->setValue(30);
    m_contrastSlider->setValue(110);
    m_brightnessSlider->setValue(0);
    
    emit resetToDefaults();
}

void CameraControls::onPresetSelected()
{
    QString preset = m_presetCombo->currentText();
    loadPreset(preset);
}

void CameraControls::loadPreset(const QString& presetName)
{
    // Load different presets with optimized settings
    if (presetName == "Portrait") {
        m_noiseReductionSlider->setValue(70);
        m_sharpeningSlider->setValue(20);
        m_saturationSlider->setValue(110);
        m_contrastSlider->setValue(105);
    } else if (presetName == "Landscape") {
        m_noiseReductionSlider->setValue(30);
        m_sharpeningSlider->setValue(50);
        m_saturationSlider->setValue(130);
        m_contrastSlider->setValue(120);
    } else if (presetName == "Low Light") {
        m_noiseReductionSlider->setValue(80);
        m_sharpeningSlider->setValue(10);
        m_saturationSlider->setValue(100);
        m_contrastSlider->setValue(115);
    } else if (presetName == "Professional") {
        m_noiseReductionSlider->setValue(60);
        m_sharpeningSlider->setValue(40);
        m_saturationSlider->setValue(115);
        m_contrastSlider->setValue(110);
        m_vibranceSlider->setValue(25);
    }
    
    onEnhancementSettingChanged();
}
