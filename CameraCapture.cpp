
#include "CameraCapture.h"
#include <QCameraDevice>
#include <QMediaDevices>
#include <QVideoFrame>
#include <QDebug>
#include <opencv2/imgproc.hpp>

CameraCapture::CameraCapture(QObject *parent)
    : QObject(parent)
    , m_camera(nullptr)
    , m_captureSession(nullptr)
    , m_videoSink(nullptr)
    , m_useOpenCV(false)
    , m_imageProcessor(nullptr)
    , m_frameTimer(nullptr)
    , m_isCapturing(false)
    , m_previewEnabled(true)
    , m_realTimeProcessing(true)
    , m_isInitialized(false)
{
    m_imageProcessor = new ImageProcessor(this);
    m_frameTimer = new QTimer(this);
    m_frameTimer->setInterval(33); // ~30 FPS
    connect(m_frameTimer, &QTimer::timeout, this, &CameraCapture::processFrame);
}

CameraCapture::~CameraCapture()
{
    stopCapture();
    delete m_camera;
    delete m_captureSession;
    delete m_videoSink;
}

bool CameraCapture::initialize()
{
    if (m_isInitialized) {
        return true;
    }
    
    // Try Qt Camera first, fallback to OpenCV
    if (initializeQtCamera()) {
        m_useOpenCV = false;
        m_isInitialized = true;
        return true;
    } else {
        qDebug() << "Qt Camera failed, trying OpenCV...";
        setupOpenCVCapture();
        m_useOpenCV = true;
        m_isInitialized = true;
        return true;
    }
}

bool CameraCapture::initializeQtCamera()
{
    try {
        // Get available cameras
        const QList<QCameraDevice> cameras = QMediaDevices::videoInputs();
        if (cameras.isEmpty()) {
            emit errorOccurred("No cameras found");
            return false;
        }
        
        // Create camera with default device
        m_camera = new QCamera(cameras.first(), this);
        
        // Create capture session
        m_captureSession = new QMediaCaptureSession(this);
        m_captureSession->setCamera(m_camera);
        
        // Create video sink for frame processing
        m_videoSink = new QVideoSink(this);
        m_captureSession->setVideoSink(m_videoSink);
        
        // Connect signals
        connect(m_videoSink, &QVideoSink::videoFrameChanged,
                this, &CameraCapture::onVideoFrameChanged);
        connect(m_camera, &QCamera::errorOccurred,
                this, &CameraCapture::onCameraErrorOccurred);
        
        return true;
    } catch (const std::exception& e) {
        qDebug() << "Qt Camera initialization failed:" << e.what();
        return false;
    }
}

void CameraCapture::setupOpenCVCapture()
{
    // Try to open default camera
    if (!m_cvCapture.open(0)) {
        emit errorOccurred("Failed to open camera with OpenCV");
        return;
    }
    
    // Set optimal settings for quality
    m_cvCapture.set(cv::CAP_PROP_FRAME_WIDTH, 1920);
    m_cvCapture.set(cv::CAP_PROP_FRAME_HEIGHT, 1080);
    m_cvCapture.set(cv::CAP_PROP_FPS, 30);
    m_cvCapture.set(cv::CAP_PROP_BUFFERSIZE, 1);
    
    // Enable auto-exposure and auto-white balance initially
    m_cvCapture.set(cv::CAP_PROP_AUTO_EXPOSURE, 0.75);
    m_cvCapture.set(cv::CAP_PROP_AUTO_WB, 1);
}

bool CameraCapture::startCapture()
{
    if (!m_isInitialized) {
        if (!initialize()) {
            return false;
        }
    }
    
    if (m_isCapturing) {
        return true;
    }
    
    if (m_useOpenCV) {
        if (!m_cvCapture.isOpened()) {
            setupOpenCVCapture();
        }
        m_frameTimer->start();
    } else {
        if (m_camera) {
            m_camera->start();
        }
    }
    
    m_isCapturing = true;
    emit cameraStatusChanged(true);
    return true;
}

void CameraCapture::stopCapture()
{
    if (!m_isCapturing) {
        return;
    }
    
    if (m_useOpenCV) {
        m_frameTimer->stop();
        if (m_cvCapture.isOpened()) {
            m_cvCapture.release();
        }
    } else {
        if (m_camera) {
            m_camera->stop();
        }
    }
    
    m_isCapturing = false;
    emit cameraStatusChanged(false);
}

void CameraCapture::processFrame()
{
    if (!m_useOpenCV || !m_cvCapture.isOpened()) {
        return;
    }
    
    cv::Mat frame;
    if (!m_cvCapture.read(frame)) {
        return;
    }
    
    if (frame.empty()) {
        return;
    }
    
    m_currentFrame = frame.clone();
    
    if (m_previewEnabled) {
        emit frameReady(m_currentFrame);
    }
    
    if (m_realTimeProcessing) {
        m_processedFrame = m_imageProcessor->processImage(m_currentFrame, m_enhancementSettings);
        emit processedFrameReady(m_processedFrame);
    }
}

void CameraCapture::onVideoFrameChanged(const QVideoFrame& frame)
{
    if (!m_previewEnabled && !m_realTimeProcessing) {
        return;
    }
    
    cv::Mat mat = qVideoFrameToMat(frame);
    if (mat.empty()) {
        return;
    }
    
    m_currentFrame = mat.clone();
    
    if (m_previewEnabled) {
        emit frameReady(m_currentFrame);
    }
    
    if (m_realTimeProcessing) {
        m_processedFrame = m_imageProcessor->processImage(m_currentFrame, m_enhancementSettings);
        emit processedFrameReady(m_processedFrame);
    }
}

cv::Mat CameraCapture::qVideoFrameToMat(const QVideoFrame& frame)
{
    if (!frame.isValid()) {
        return cv::Mat();
    }
    
    QVideoFrame clonedFrame(frame);
    clonedFrame.map(QVideoFrame::ReadOnly);
    
    const QImage image = clonedFrame.toImage();
    clonedFrame.unmap();
    
    if (image.isNull()) {
        return cv::Mat();
    }
    
    // Convert QImage to cv::Mat
    QImage rgbImage = image.convertToFormat(QImage::Format_RGB888);
    cv::Mat mat(rgbImage.height(), rgbImage.width(), CV_8UC3, 
                (void*)rgbImage.constBits(), rgbImage.bytesPerLine());
    
    cv::Mat result;
    cv::cvtColor(mat, result, cv::COLOR_RGB2BGR);
    
    return result.clone();
}

void CameraCapture::setCameraSettings(const CameraSettings& settings)
{
    m_settings = settings;
    applyCameraSettings();
}

void CameraCapture::applyCameraSettings()
{
    if (m_useOpenCV && m_cvCapture.isOpened()) {
        // Apply OpenCV settings
        if (!m_settings.autoExposure) {
            setExposure(m_settings.exposure);
        }
        
        if (!m_settings.autoISO) {
            setISO(m_settings.iso);
        }
        
        if (!m_settings.autoWhiteBalance) {
            setWhiteBalance(m_settings.whiteBalance);
        }
        
        if (!m_settings.autoFocus) {
            setFocus(m_settings.focus);
        }
        
        setResolution(m_settings.resolution);
        setFrameRate(m_settings.frameRate);
        
    } else if (m_camera) {
        // Apply Qt Camera settings
        updateCameraProperties();
    }
}

void CameraCapture::setExposure(double exposure)
{
    if (m_useOpenCV && m_cvCapture.isOpened()) {
        // Convert EV to exposure value (camera dependent)
        double exposureValue = std::pow(2.0, exposure);
        m_cvCapture.set(cv::CAP_PROP_AUTO_EXPOSURE, 0.25); // Manual mode
        m_cvCapture.set(cv::CAP_PROP_EXPOSURE, exposureValue);
    }
}

void CameraCapture::setISO(int iso)
{
    if (m_useOpenCV && m_cvCapture.isOpened()) {
        // ISO mapping (camera dependent)
        double isoValue = iso / 100.0;
        m_cvCapture.set(cv::CAP_PROP_ISO_SPEED, iso);
    }
}

void CameraCapture::setWhiteBalance(double temperature)
{
    if (m_useOpenCV && m_cvCapture.isOpened()) {
        m_cvCapture.set(cv::CAP_PROP_AUTO_WB, 0); // Manual WB
        m_cvCapture.set(cv::CAP_PROP_WB_TEMPERATURE, temperature);
    }
}

void CameraCapture::setFocus(double focus)
{
    if (m_useOpenCV && m_cvCapture.isOpened()) {
        m_cvCapture.set(cv::CAP_PROP_AUTOFOCUS, 0); // Manual focus
        m_cvCapture.set(cv::CAP_PROP_FOCUS, focus);
    }
}

void CameraCapture::setResolution(int resolution)
{
    if (m_useOpenCV && m_cvCapture.isOpened()) {
        switch (resolution) {
            case 720:
                m_cvCapture.set(cv::CAP_PROP_FRAME_WIDTH, 1280);
                m_cvCapture.set(cv::CAP_PROP_FRAME_HEIGHT, 720);
                break;
            case 1080:
                m_cvCapture.set(cv::CAP_PROP_FRAME_WIDTH, 1920);
                m_cvCapture.set(cv::CAP_PROP_FRAME_HEIGHT, 1080);
                break;
            case 1440:
                m_cvCapture.set(cv::CAP_PROP_FRAME_WIDTH, 2560);
                m_cvCapture.set(cv::CAP_PROP_FRAME_HEIGHT, 1440);
                break;
            case 2160:
                m_cvCapture.set(cv::CAP_PROP_FRAME_WIDTH, 3840);
                m_cvCapture.set(cv::CAP_PROP_FRAME_HEIGHT, 2160);
                break;
        }
    }
}

void CameraCapture::setFrameRate(int frameRate)
{
    if (m_useOpenCV && m_cvCapture.isOpened()) {
        m_cvCapture.set(cv::CAP_PROP_FPS, frameRate);
        m_frameTimer->setInterval(1000 / frameRate);
    }
}

void CameraCapture::updateCameraProperties()
{
    // Qt Camera property updates would go here
    // This is more complex and camera-dependent
}

void CameraCapture::setEnhancementSettings(const ImageEnhancementSettings& settings)
{
    m_enhancementSettings = settings;
}

QList<QCameraDevice> CameraCapture::getAvailableCameras() const
{
    return QMediaDevices::videoInputs();
}

void CameraCapture::selectCamera(int index)
{
    const QList<QCameraDevice> cameras = getAvailableCameras();
    if (index >= 0 && index < cameras.size()) {
        stopCapture();
        
        if (!m_useOpenCV && m_camera) {
            delete m_camera;
            m_camera = new QCamera(cameras[index], this);
            m_captureSession->setCamera(m_camera);
            connect(m_camera, &QCamera::errorOccurred,
                    this, &CameraCapture::onCameraErrorOccurred);
        } else {
            // For OpenCV, try to open specific camera
            m_cvCapture.release();
            m_cvCapture.open(index);
        }
    }
}

QString CameraCapture::getCurrentCameraName() const
{
    if (!m_useOpenCV && m_camera) {
        return m_camera->cameraDevice().description();
    }
    return QString("Camera %1").arg(0);
}

void CameraCapture::capturePhoto()
{
    if (!m_currentFrame.empty()) {
        cv::Mat photo = m_realTimeProcessing ? m_processedFrame : m_currentFrame;
        emit photoCapture(photo);
    }
}

void CameraCapture::onCameraErrorOccurred()
{
    if (m_camera) {
        emit errorOccurred(QString("Camera error: %1").arg(static_cast<int>(m_camera->error())));
    }
}
