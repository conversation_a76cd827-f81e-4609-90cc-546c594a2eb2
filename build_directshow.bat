@echo off
echo Building DirectShow Professional Camera Application...

echo Compiling with MinGW g++...
g++ -std=c++17 -O2 -static-libgcc -static-libstdc++ -o camera_directshow.exe camera_directshow.cpp -lstrmiids -lole32 -loleaut32 -luuid -lgdi32 -luser32 -lkernel32

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    echo.
    echo This might be because DirectShow headers are not available in MinGW.
    echo Let me try a simpler approach...
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable created: camera_directshow.exe

if exist "camera_directshow.exe" (
    echo File size:
    dir camera_directshow.exe | findstr camera_directshow.exe
    echo.
    echo You can now run: camera_directshow.exe
) else (
    echo Error: Executable not found!
)

pause
