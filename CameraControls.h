#ifndef CAMERACONTROLS_H
#define CAMERACONTROLS_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QSlider>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QCheckBox>
#include <QComboBox>
#include <QLabel>
#include <QPushButton>
#include <QTabWidget>
#include "CameraCapture.h"
#include "ImageProcessor.h"

class CameraControls : public QWidget
{
    Q_OBJECT

public:
    explicit CameraControls(QWidget *parent = nullptr);
    
    void setCameraCapture(CameraCapture* capture);
    void updateFromSettings();

signals:
    void cameraSettingsChanged(const CameraSettings& settings);
    void enhancementSettingsChanged(const ImageEnhancementSettings& settings);
    void capturePhoto();
    void resetToDefaults();

private slots:
    void onCameraSettingChanged();
    void onEnhancementSettingChanged();
    void onResetDefaults();
    void onPresetSelected();

private:
    void setupUI();
    void setupCameraTab();
    void setupEnhancementTab();
    void setupPresetsTab();
    void connectSignals();
    void updateCameraSettings();
    void updateEnhancementSettings();
    void loadPreset(const QString& presetName);
    
    // Camera capture reference
    CameraCapture* m_cameraCapture;
    
    // Main layout
    QVBoxLayout* m_mainLayout;
    QTabWidget* m_tabWidget;
    
    // Camera controls tab
    QWidget* m_cameraTab;
    QGroupBox* m_exposureGroup;
    QGroupBox* m_colorGroup;
    QGroupBox* m_focusGroup;
    QGroupBox* m_qualityGroup;
    
    // Camera controls
    QCheckBox* m_autoExposureCheck;
    QSlider* m_exposureSlider;
    QLabel* m_exposureLabel;
    
    QCheckBox* m_autoISOCheck;
    QSpinBox* m_isoSpin;
    
    QCheckBox* m_autoWBCheck;
    QSpinBox* m_whiteBalanceSpin;
    
    QCheckBox* m_autoFocusCheck;
    QSlider* m_focusSlider;
    QLabel* m_focusLabel;
    
    QComboBox* m_resolutionCombo;
    QComboBox* m_frameRateCombo;
    QCheckBox* m_stabilizationCheck;
    QCheckBox* m_hdrCheck;
    
    // Enhancement controls tab
    QWidget* m_enhancementTab;
    QGroupBox* m_noiseGroup;
    QGroupBox* m_sharpenGroup;
    QGroupBox* m_colorEnhanceGroup;
    QGroupBox* m_toneGroup;
    QGroupBox* m_advancedGroup;
    
    // Enhancement controls
    QSlider* m_noiseReductionSlider;
    QLabel* m_noiseReductionLabel;
    
    QSlider* m_sharpeningSlider;
    QLabel* m_sharpeningLabel;
    QDoubleSpinBox* m_unsharpAmountSpin;
    QDoubleSpinBox* m_unsharpRadiusSpin;
    
    QSlider* m_saturationSlider;
    QLabel* m_saturationLabel;
    QSlider* m_vibranceSlider;
    QLabel* m_vibranceLabel;
    QSlider* m_contrastSlider;
    QLabel* m_contrastLabel;
    QSlider* m_brightnessSlider;
    QLabel* m_brightnessLabel;
    
    QSlider* m_shadowsSlider;
    QLabel* m_shadowsLabel;
    QSlider* m_highlightsSlider;
    QLabel* m_highlightsLabel;
    QDoubleSpinBox* m_gammaSpin;
    
    QSlider* m_temperatureSlider;
    QLabel* m_temperatureLabel;
    QSlider* m_tintSlider;
    QLabel* m_tintLabel;
    
    QCheckBox* m_enableHDRCheck;
    QSlider* m_hdrStrengthSlider;
    QLabel* m_hdrStrengthLabel;
    QCheckBox* m_lensCorrectCheck;
    QSlider* m_vignettingSlider;
    QLabel* m_vignettingLabel;
    
    // Presets tab
    QWidget* m_presetsTab;
    QComboBox* m_presetCombo;
    QPushButton* m_loadPresetBtn;
    QPushButton* m_savePresetBtn;
    QPushButton* m_resetBtn;
    
    // Action buttons
    QHBoxLayout* m_buttonLayout;
    QPushButton* m_captureBtn;
    QPushButton* m_resetDefaultsBtn;
    
    // Current settings
    CameraSettings m_currentCameraSettings;
    ImageEnhancementSettings m_currentEnhancementSettings;
    
    // Helper functions
    QSlider* createSlider(int min, int max, int value, int tickInterval = 0);
    QLabel* createValueLabel(const QString& text);
    void addControlRow(QGridLayout* layout, int row, const QString& label, QWidget* control, QLabel* valueLabel = nullptr);
    QString formatSliderValue(int value, double scale = 1.0, const QString& suffix = "");
};

#endif // CAMERACONTROLS_H
