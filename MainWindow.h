#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QSplitter>
#include <QLabel>
#include <QScrollArea>
#include <QMenuBar>
#include <QStatusBar>
#include <QAction>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QPixmap>
#include <QComboBox>
#include <QPushButton>
#include <QCheckBox>
#include <QProgressBar>
#include <opencv2/opencv.hpp>
#include "CameraCapture.h"
#include "CameraControls.h"
#include "ImageProcessor.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onFrameReady(const cv::Mat& frame);
    void onProcessedFrameReady(const cv::Mat& frame);
    void onPhotoCapture(const cv::Mat& image);
    void onCameraError(const QString& error);
    void onCameraStatusChanged(bool available);
    
    void onCameraSettingsChanged(const CameraSettings& settings);
    void onEnhancementSettingsChanged(const ImageEnhancementSettings& settings);
    
    void onStartStopCamera();
    void onCapturePhoto();
    void onSaveImage();
    void onLoadImage();
    void onToggleProcessing();
    void onSelectCamera();
    void onShowAbout();
    void onToggleFullscreen();
    void onZoomIn();
    void onZoomOut();
    void onZoomFit();
    void onZoomActual();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupCentralWidget();
    void connectSignals();
    void updatePreview(const cv::Mat& frame, bool isProcessed = false);
    void updateStatusBar();
    void saveImageToFile(const cv::Mat& image);
    QPixmap matToQPixmap(const cv::Mat& mat);
    void scalePreview();
    void showError(const QString& message);
    void showInfo(const QString& message);
    
    // UI Components
    QWidget* m_centralWidget;
    QHBoxLayout* m_mainLayout;
    QSplitter* m_splitter;
    
    // Preview area
    QWidget* m_previewWidget;
    QVBoxLayout* m_previewLayout;
    QScrollArea* m_scrollArea;
    QLabel* m_previewLabel;
    QHBoxLayout* m_previewControlsLayout;
    QComboBox* m_cameraCombo;
    QPushButton* m_startStopBtn;
    QPushButton* m_captureBtn;
    QCheckBox* m_processingCheck;
    QPushButton* m_saveBtn;
    
    // Zoom controls
    QHBoxLayout* m_zoomLayout;
    QPushButton* m_zoomInBtn;
    QPushButton* m_zoomOutBtn;
    QPushButton* m_zoomFitBtn;
    QPushButton* m_zoomActualBtn;
    QLabel* m_zoomLabel;
    
    // Controls panel
    CameraControls* m_cameraControls;
    
    // Core components
    CameraCapture* m_cameraCapture;
    ImageProcessor* m_imageProcessor;
    
    // Menu and status
    QMenuBar* m_menuBar;
    QStatusBar* m_statusBar;
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
    QLabel* m_fpsLabel;
    QLabel* m_resolutionLabel;
    
    // Actions
    QAction* m_startCameraAction;
    QAction* m_stopCameraAction;
    QAction* m_captureAction;
    QAction* m_saveAction;
    QAction* m_loadAction;
    QAction* m_exitAction;
    QAction* m_aboutAction;
    QAction* m_fullscreenAction;
    
    // State
    bool m_isFullscreen;
    double m_zoomFactor;
    cv::Mat m_currentFrame;
    cv::Mat m_lastCapturedImage;
    QTimer* m_fpsTimer;
    int m_frameCount;
    
    // Constants
    static const double ZOOM_STEP;
    static const double MIN_ZOOM;
    static const double MAX_ZOOM;
};

#endif // MAINWINDOW_H
