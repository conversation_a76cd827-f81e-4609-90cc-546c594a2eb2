#ifndef IMAGEPROCESSOR_H
#define IMAGEPROCESSOR_H

#include <opencv2/opencv.hpp>
#include <QObject>

struct ImageEnhancementSettings {
    // Noise reduction
    double noiseReduction = 0.5;
    
    // Sharpening
    double sharpening = 0.3;
    double unsharpMaskAmount = 1.5;
    double unsharpMaskRadius = 1.0;
    
    // Color enhancement
    double saturation = 1.2;
    double vibrance = 0.3;
    double contrast = 1.1;
    double brightness = 0.0;
    
    // Tone mapping
    double shadows = 0.0;
    double highlights = 0.0;
    double gamma = 1.0;
    
    // Color correction
    double temperature = 0.0;  // -100 to 100
    double tint = 0.0;         // -100 to 100
    
    // Advanced
    bool enableHDR = false;
    double hdrStrength = 0.5;
    bool enableLensCorrection = true;
    double vignettingCorrection = 0.3;
};

class ImageProcessor : public QObject
{
    Q_OBJECT

public:
    explicit ImageProcessor(QObject *parent = nullptr);
    
    // Main processing function
    cv::Mat processImage(const cv::Mat& input, const ImageEnhancementSettings& settings);
    
    // Individual enhancement functions
    cv::Mat reduceNoise(const cv::Mat& image, double strength);
    cv::Mat sharpenImage(const cv::Mat& image, double amount, double radius);
    cv::Mat enhanceColors(const cv::Mat& image, double saturation, double vibrance, double contrast, double brightness);
    cv::Mat adjustTones(const cv::Mat& image, double shadows, double highlights, double gamma);
    cv::Mat correctColors(const cv::Mat& image, double temperature, double tint);
    cv::Mat applyHDR(const cv::Mat& image, double strength);
    cv::Mat correctLens(const cv::Mat& image, double vignettingCorrection);
    
    // Utility functions
    cv::Mat convertToDisplayFormat(const cv::Mat& image);
    cv::Mat unsharpMask(const cv::Mat& image, double amount, double radius);
    
private:
    // Helper functions
    cv::Mat adjustGamma(const cv::Mat& image, double gamma);
    cv::Mat adjustSaturation(const cv::Mat& image, double saturation);
    cv::Mat adjustVibrance(const cv::Mat& image, double vibrance);
    cv::Mat correctVignetting(const cv::Mat& image, double strength);
    cv::Mat temperatureAdjustment(const cv::Mat& image, double temperature);
    cv::Mat tintAdjustment(const cv::Mat& image, double tint);
    
    // Advanced processing
    cv::Mat localContrastEnhancement(const cv::Mat& image);
    cv::Mat edgePreservingFilter(const cv::Mat& image, double strength);
};

#endif // IMAGEPROCESSOR_H
