@echo off
echo Building Professional Camera Application...

REM Create build directory
if not exist "build" mkdir build
cd build

REM Configure with CMake
echo Configuring with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable location: build\Release\camera.exe

REM Copy executable to root directory for easy access
if exist "Release\camera.exe" (
    copy "Release\camera.exe" "..\camera.exe"
    echo Copied camera.exe to root directory
)

pause
