#ifndef CAMERACAPTURE_H
#define CAMERACAPTURE_H

#include <QObject>
#include <QTimer>
#include <QCamera>
#include <QMediaCaptureSession>
#include <QVideoSink>
#include <QVideoFrame>
#include <opencv2/opencv.hpp>
#include "ImageProcessor.h"

struct CameraSettings {
    // Exposure settings
    double exposure = 0.0;          // -3.0 to 3.0 (EV)
    bool autoExposure = true;
    
    // ISO settings
    int iso = 100;                  // 100-6400
    bool autoISO = true;
    
    // White balance
    double whiteBalance = 5500;     // 2000-10000K
    bool autoWhiteBalance = true;
    
    // Focus
    double focus = 0.5;             // 0.0-1.0 (near to far)
    bool autoFocus = true;
    
    // Image quality
    int resolution = 1080;          // 720, 1080, 1440, 2160
    int frameRate = 30;             // 15, 24, 30, 60
    
    // Advanced
    double digitalZoom = 1.0;       // 1.0-10.0
    bool stabilization = true;
    bool hdr = false;
};

class CameraCapture : public QObject
{
    Q_OBJECT

public:
    explicit CameraCapture(QObject *parent = nullptr);
    ~CameraCapture();

    // Camera control
    bool initialize();
    bool startCapture();
    void stopCapture();
    bool isCapturing() const { return m_isCapturing; }
    
    // Settings
    void setCameraSettings(const CameraSettings& settings);
    CameraSettings getCameraSettings() const { return m_settings; }
    
    // Enhancement settings
    void setEnhancementSettings(const ImageEnhancementSettings& settings);
    ImageEnhancementSettings getEnhancementSettings() const { return m_enhancementSettings; }
    
    // Camera information
    QList<QCameraDevice> getAvailableCameras() const;
    void selectCamera(int index);
    QString getCurrentCameraName() const;
    
    // Capture functions
    void capturePhoto();
    void startVideoRecording(const QString& filename);
    void stopVideoRecording();
    
    // Preview control
    void enablePreview(bool enable) { m_previewEnabled = enable; }
    bool isPreviewEnabled() const { return m_previewEnabled; }
    
    // Image processing
    void enableRealTimeProcessing(bool enable) { m_realTimeProcessing = enable; }
    bool isRealTimeProcessingEnabled() const { return m_realTimeProcessing; }

signals:
    void frameReady(const cv::Mat& frame);
    void processedFrameReady(const cv::Mat& frame);
    void photoCapture(const cv::Mat& image);
    void errorOccurred(const QString& error);
    void cameraStatusChanged(bool available);

private slots:
    void onVideoFrameChanged(const QVideoFrame& frame);
    void onCameraErrorOccurred();
    void processFrame();

private:
    // Qt Camera components
    QCamera* m_camera;
    QMediaCaptureSession* m_captureSession;
    QVideoSink* m_videoSink;
    
    // OpenCV components
    cv::VideoCapture m_cvCapture;
    bool m_useOpenCV;
    
    // Processing
    ImageProcessor* m_imageProcessor;
    QTimer* m_frameTimer;
    
    // Settings
    CameraSettings m_settings;
    ImageEnhancementSettings m_enhancementSettings;
    
    // State
    bool m_isCapturing;
    bool m_previewEnabled;
    bool m_realTimeProcessing;
    bool m_isInitialized;
    
    // Current frame data
    cv::Mat m_currentFrame;
    cv::Mat m_processedFrame;
    
    // Helper functions
    cv::Mat qVideoFrameToMat(const QVideoFrame& frame);
    void applyCameraSettings();
    void setupOpenCVCapture();
    bool initializeQtCamera();
    void updateCameraProperties();
    
    // Camera parameter mapping
    void setExposure(double exposure);
    void setISO(int iso);
    void setWhiteBalance(double temperature);
    void setFocus(double focus);
    void setResolution(int resolution);
    void setFrameRate(int frameRate);
};

#endif // CAMERACAPTURE_H
