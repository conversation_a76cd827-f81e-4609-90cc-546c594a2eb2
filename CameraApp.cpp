#include "CameraApp.h"
#include "MainWindow.h"
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>

CameraApp::CameraApp(int &argc, char **argv)
    : QApplication(argc, argv), m_mainWindow(nullptr)
{
    setupApplicationProperties();
}

CameraApp::~CameraApp()
{
    delete m_mainWindow;
}

void CameraApp::setupApplicationProperties()
{
    setApplicationName("Professional Camera");
    setApplicationVersion("1.0.0");
    setOrganizationName("Professional Camera Studio");
    setApplicationDisplayName("Professional Camera");
}

bool CameraApp::initialize()
{
    setupStyle();
    
    m_mainWindow = new MainWindow();
    if (!m_mainWindow) {
        return false;
    }
    
    m_mainWindow->show();
    return true;
}

void CameraApp::setupStyle()
{
    // Set a modern dark style for professional look
    setStyle(QStyleFactory::create("Fusion"));
    
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    setPalette(darkPalette);

    // Set custom stylesheet for professional appearance
    setStyleSheet(
        "QMainWindow { background-color: #2b2b2b; }"
        "QWidget { background-color: #2b2b2b; color: white; }"
        "QPushButton { "
        "    background-color: #404040; "
        "    border: 1px solid #555555; "
        "    border-radius: 4px; "
        "    padding: 8px 16px; "
        "    font-weight: bold; "
        "}"
        "QPushButton:hover { background-color: #505050; }"
        "QPushButton:pressed { background-color: #303030; }"
        "QSlider::groove:horizontal { "
        "    border: 1px solid #999999; "
        "    height: 8px; "
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #B1B1B1, stop:1 #c4c4c4); "
        "    margin: 2px 0; "
        "}"
        "QSlider::handle:horizontal { "
        "    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #b4b4b4, stop:1 #8f8f8f); "
        "    border: 1px solid #5c5c5c; "
        "    width: 18px; "
        "    margin: -2px 0; "
        "    border-radius: 3px; "
        "}"
    );
}

#include "CameraApp.moc"
