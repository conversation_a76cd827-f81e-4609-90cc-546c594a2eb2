#include "ImageProcessor.h"
#include <opencv2/imgproc.hpp>
#include <opencv2/photo.hpp>
#include <algorithm>
#include <cmath>

ImageProcessor::ImageProcessor(QObject *parent)
    : QObject(parent)
{
}

cv::Mat ImageProcessor::processImage(const cv::Mat& input, const ImageEnhancementSettings& settings)
{
    if (input.empty()) {
        return cv::Mat();
    }
    
    cv::Mat result = input.clone();
    
    // Convert to floating point for better processing
    cv::Mat floatImage;
    result.convertTo(floatImage, CV_32F, 1.0/255.0);
    
    // 1. Noise reduction (first to clean the image)
    if (settings.noiseReduction > 0.0) {
        floatImage = reduceNoise(floatImage, settings.noiseReduction);
    }
    
    // 2. Lens correction
    if (settings.enableLensCorrection) {
        floatImage = correctLens(floatImage, settings.vignettingCorrection);
    }
    
    // 3. Color correction
    if (settings.temperature != 0.0 || settings.tint != 0.0) {
        floatImage = correctColors(floatImage, settings.temperature, settings.tint);
    }
    
    // 4. Tone adjustments
    if (settings.shadows != 0.0 || settings.highlights != 0.0 || settings.gamma != 1.0) {
        floatImage = adjustTones(floatImage, settings.shadows, settings.highlights, settings.gamma);
    }
    
    // 5. Color enhancement
    if (settings.saturation != 1.0 || settings.vibrance != 0.0 || 
        settings.contrast != 1.0 || settings.brightness != 0.0) {
        floatImage = enhanceColors(floatImage, settings.saturation, settings.vibrance, 
                                 settings.contrast, settings.brightness);
    }
    
    // 6. HDR processing
    if (settings.enableHDR) {
        floatImage = applyHDR(floatImage, settings.hdrStrength);
    }
    
    // 7. Sharpening (last to avoid amplifying noise)
    if (settings.sharpening > 0.0) {
        floatImage = unsharpMask(floatImage, settings.unsharpMaskAmount, settings.unsharpMaskRadius);
    }
    
    // Convert back to 8-bit
    cv::Mat finalResult;
    floatImage.convertTo(finalResult, CV_8U, 255.0);
    
    return finalResult;
}

cv::Mat ImageProcessor::reduceNoise(const cv::Mat& image, double strength)
{
    cv::Mat result;
    
    // Use Non-local Means Denoising for color images
    if (image.channels() == 3) {
        cv::Mat temp;
        image.convertTo(temp, CV_8U, 255.0);
        cv::fastNlMeansDenoisingColored(temp, result, 
                                      static_cast<float>(strength * 10), 
                                      static_cast<float>(strength * 10), 
                                      7, 21);
        result.convertTo(result, CV_32F, 1.0/255.0);
    } else {
        cv::Mat temp;
        image.convertTo(temp, CV_8U, 255.0);
        cv::fastNlMeansDenoising(temp, result, 
                               static_cast<float>(strength * 10), 
                               7, 21);
        result.convertTo(result, CV_32F, 1.0/255.0);
    }
    
    return result;
}

cv::Mat ImageProcessor::unsharpMask(const cv::Mat& image, double amount, double radius)
{
    cv::Mat blurred;
    int kernelSize = static_cast<int>(radius * 6) | 1; // Ensure odd number
    cv::GaussianBlur(image, blurred, cv::Size(kernelSize, kernelSize), radius);
    
    cv::Mat mask = image - blurred;
    cv::Mat result = image + amount * mask;
    
    return result;
}

cv::Mat ImageProcessor::enhanceColors(const cv::Mat& image, double saturation, 
                                    double vibrance, double contrast, double brightness)
{
    cv::Mat result = image.clone();
    
    // Brightness adjustment
    if (brightness != 0.0) {
        result += brightness;
    }
    
    // Contrast adjustment
    if (contrast != 1.0) {
        result = (result - 0.5) * contrast + 0.5;
    }
    
    // Saturation adjustment
    if (saturation != 1.0) {
        result = adjustSaturation(result, saturation);
    }
    
    // Vibrance adjustment
    if (vibrance != 0.0) {
        result = adjustVibrance(result, vibrance);
    }
    
    return result;
}

cv::Mat ImageProcessor::adjustSaturation(const cv::Mat& image, double saturation)
{
    cv::Mat hsv;
    cv::cvtColor(image, hsv, cv::COLOR_BGR2HSV);
    
    std::vector<cv::Mat> channels;
    cv::split(hsv, channels);
    
    // Adjust saturation channel
    channels[1] *= saturation;
    
    cv::merge(channels, hsv);
    cv::Mat result;
    cv::cvtColor(hsv, result, cv::COLOR_HSV2BGR);
    
    return result;
}

cv::Mat ImageProcessor::adjustVibrance(const cv::Mat& image, double vibrance)
{
    cv::Mat result = image.clone();
    
    // Vibrance affects less saturated colors more than highly saturated ones
    cv::Mat hsv;
    cv::cvtColor(image, hsv, cv::COLOR_BGR2HSV);
    
    std::vector<cv::Mat> channels;
    cv::split(hsv, channels);
    
    cv::Mat satMask = 1.0 - channels[1]; // Inverse saturation mask
    channels[1] += vibrance * satMask * 0.5;
    
    cv::merge(channels, hsv);
    cv::cvtColor(hsv, result, cv::COLOR_HSV2BGR);
    
    return result;
}

cv::Mat ImageProcessor::adjustTones(const cv::Mat& image, double shadows, double highlights, double gamma)
{
    cv::Mat result = image.clone();
    
    // Gamma correction
    if (gamma != 1.0) {
        result = adjustGamma(result, gamma);
    }
    
    // Shadow/highlight adjustment using luminance masks
    if (shadows != 0.0 || highlights != 0.0) {
        cv::Mat gray;
        cv::cvtColor(result, gray, cv::COLOR_BGR2GRAY);
        
        // Create shadow and highlight masks
        cv::Mat shadowMask = 1.0 - gray;
        cv::Mat highlightMask = gray;
        
        // Apply adjustments
        if (shadows != 0.0) {
            result += shadows * 0.3 * cv::Scalar(1, 1, 1);
            cv::multiply(result, shadowMask, result);
        }
        
        if (highlights != 0.0) {
            cv::Mat highlightAdj = result - highlights * 0.3 * cv::Scalar(1, 1, 1);
            cv::multiply(highlightAdj, highlightMask, highlightAdj);
            result = result + highlightAdj;
        }
    }
    
    return result;
}

cv::Mat ImageProcessor::adjustGamma(const cv::Mat& image, double gamma)
{
    cv::Mat result;
    cv::pow(image, 1.0/gamma, result);
    return result;
}

cv::Mat ImageProcessor::correctColors(const cv::Mat& image, double temperature, double tint)
{
    cv::Mat result = image.clone();
    
    if (temperature != 0.0) {
        result = temperatureAdjustment(result, temperature);
    }
    
    if (tint != 0.0) {
        result = tintAdjustment(result, tint);
    }
    
    return result;
}

cv::Mat ImageProcessor::temperatureAdjustment(const cv::Mat& image, double temperature)
{
    cv::Mat result = image.clone();
    
    // Temperature adjustment affects blue/yellow balance
    double factor = temperature / 100.0;
    
    std::vector<cv::Mat> channels;
    cv::split(result, channels);
    
    if (factor > 0) { // Warmer (more yellow/red)
        channels[0] *= (1.0 - factor * 0.2); // Reduce blue
        channels[2] *= (1.0 + factor * 0.1); // Increase red
    } else { // Cooler (more blue)
        channels[0] *= (1.0 - factor * 0.2); // Increase blue
        channels[2] *= (1.0 + factor * 0.1); // Reduce red
    }
    
    cv::merge(channels, result);
    return result;
}

cv::Mat ImageProcessor::tintAdjustment(const cv::Mat& image, double tint)
{
    cv::Mat result = image.clone();
    
    // Tint adjustment affects green/magenta balance
    double factor = tint / 100.0;
    
    std::vector<cv::Mat> channels;
    cv::split(result, channels);
    
    if (factor > 0) { // More green
        channels[1] *= (1.0 + factor * 0.2);
    } else { // More magenta
        channels[0] *= (1.0 - factor * 0.1); // Reduce blue
        channels[2] *= (1.0 - factor * 0.1); // Reduce red
    }
    
    cv::merge(channels, result);
    return result;
}

cv::Mat ImageProcessor::applyHDR(const cv::Mat& image, double strength)
{
    cv::Mat result;
    
    // Simple tone mapping for HDR effect
    cv::Mat gray;
    cv::cvtColor(image, gray, cv::COLOR_BGR2GRAY);
    
    cv::Mat blurred;
    cv::GaussianBlur(gray, blurred, cv::Size(0, 0), 30);
    
    cv::Mat mask = gray / (blurred + 0.001);
    
    std::vector<cv::Mat> channels;
    cv::split(image, channels);
    
    for (auto& channel : channels) {
        channel = channel * (1.0 - strength) + channel.mul(mask) * strength;
    }
    
    cv::merge(channels, result);
    return result;
}

cv::Mat ImageProcessor::correctLens(const cv::Mat& image, double vignettingCorrection)
{
    if (vignettingCorrection <= 0.0) {
        return image;
    }
    
    return correctVignetting(image, vignettingCorrection);
}

cv::Mat ImageProcessor::correctVignetting(const cv::Mat& image, double strength)
{
    cv::Mat result = image.clone();
    
    int centerX = image.cols / 2;
    int centerY = image.rows / 2;
    double maxDist = std::sqrt(centerX * centerX + centerY * centerY);
    
    for (int y = 0; y < image.rows; ++y) {
        for (int x = 0; x < image.cols; ++x) {
            double dist = std::sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
            double factor = 1.0 + strength * (dist / maxDist);
            
            cv::Vec3f& pixel = result.at<cv::Vec3f>(y, x);
            pixel *= factor;
        }
    }
    
    return result;
}

cv::Mat ImageProcessor::convertToDisplayFormat(const cv::Mat& image)
{
    cv::Mat result;
    if (image.type() == CV_32F || image.type() == CV_32FC3) {
        image.convertTo(result, CV_8U, 255.0);
    } else {
        result = image.clone();
    }
    return result;
}
