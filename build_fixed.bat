@echo off
echo Building BLACK SCREEN FIX Camera Application...

echo Compiling with MinGW g++...
g++ -std=c++17 -O2 -static-libgcc -static-libstdc++ -o camera_fixed.exe camera_fixed.cpp -lvfw32 -lwinmm -lgdi32 -luser32 -lkernel32

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable created: camera_fixed.exe

if exist "camera_fixed.exe" (
    echo File size:
    dir camera_fixed.exe | findstr camera_fixed.exe
    echo.
    echo BLACK SCREEN FIX VERSION READY!
    echo Run: camera_fixed.exe
    echo Then click "Fix Black Screen" button
) else (
    echo Error: Executable not found!
)

pause
