#include "MainWindow.h"
#include <QApplication>
#include <QScreen>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QFileInfo>
#include <QMenu>
#include <algorithm>

const double MainWindow::ZOOM_STEP = 1.2;
const double MainWindow::MIN_ZOOM = 0.1;
const double MainWindow::MAX_ZOOM = 5.0;

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_cameraCapture(nullptr)
    , m_imageProcessor(nullptr)
    , m_cameraControls(nullptr)
    , m_isFullscreen(false)
    , m_zoomFactor(1.0)
    , m_frameCount(0)
{
    setWindowTitle("Professional Camera - Enhanced Image Quality");
    setMinimumSize(1200, 800);
    resize(1600, 1000);
    
    // Initialize components
    m_cameraCapture = new CameraCapture(this);
    m_imageProcessor = new ImageProcessor(this);
    
    // Setup UI
    setupUI();
    setupMenuBar();
    setupStatusBar();
    connectSignals();
    
    // Initialize camera
    if (!m_cameraCapture->initialize()) {
        showError("Failed to initialize camera system");
    }
    
    // Setup FPS counter
    m_fpsTimer = new QTimer(this);
    connect(m_fpsTimer, &QTimer::timeout, this, [this]() {
        m_fpsLabel->setText(QString("FPS: %1").arg(m_frameCount));
        m_frameCount = 0;
    });
    m_fpsTimer->start(1000);
    
    updateStatusBar();
}

MainWindow::~MainWindow()
{
    if (m_cameraCapture) {
        m_cameraCapture->stopCapture();
    }
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget();
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QHBoxLayout(m_centralWidget);
    m_splitter = new QSplitter(Qt::Horizontal);
    
    setupCentralWidget();
    
    m_mainLayout->addWidget(m_splitter);
}

void MainWindow::setupCentralWidget()
{
    // Preview area
    m_previewWidget = new QWidget();
    m_previewLayout = new QVBoxLayout(m_previewWidget);
    
    // Camera selection and controls
    m_previewControlsLayout = new QHBoxLayout();
    
    m_cameraCombo = new QComboBox();
    m_cameraCombo->setMinimumWidth(200);
    
    m_startStopBtn = new QPushButton("Start Camera");
    m_startStopBtn->setStyleSheet("QPushButton { background-color: #4CAF50; font-weight: bold; padding: 8px 16px; }");
    
    m_captureBtn = new QPushButton("Capture");
    m_captureBtn->setEnabled(false);
    m_captureBtn->setStyleSheet("QPushButton { background-color: #2196F3; font-weight: bold; padding: 8px 16px; }");
    
    m_processingCheck = new QCheckBox("Real-time Processing");
    m_processingCheck->setChecked(true);
    
    m_saveBtn = new QPushButton("Save Image");
    m_saveBtn->setEnabled(false);
    
    m_previewControlsLayout->addWidget(new QLabel("Camera:"));
    m_previewControlsLayout->addWidget(m_cameraCombo);
    m_previewControlsLayout->addWidget(m_startStopBtn);
    m_previewControlsLayout->addWidget(m_captureBtn);
    m_previewControlsLayout->addWidget(m_processingCheck);
    m_previewControlsLayout->addStretch();
    m_previewControlsLayout->addWidget(m_saveBtn);
    
    // Zoom controls
    m_zoomLayout = new QHBoxLayout();
    m_zoomInBtn = new QPushButton("Zoom In");
    m_zoomOutBtn = new QPushButton("Zoom Out");
    m_zoomFitBtn = new QPushButton("Fit");
    m_zoomActualBtn = new QPushButton("100%");
    m_zoomLabel = new QLabel("100%");
    m_zoomLabel->setMinimumWidth(50);
    m_zoomLabel->setAlignment(Qt::AlignCenter);
    
    m_zoomLayout->addWidget(m_zoomInBtn);
    m_zoomLayout->addWidget(m_zoomOutBtn);
    m_zoomLayout->addWidget(m_zoomFitBtn);
    m_zoomLayout->addWidget(m_zoomActualBtn);
    m_zoomLayout->addWidget(m_zoomLabel);
    m_zoomLayout->addStretch();
    
    // Preview display
    m_scrollArea = new QScrollArea();
    m_previewLabel = new QLabel();
    m_previewLabel->setAlignment(Qt::AlignCenter);
    m_previewLabel->setStyleSheet("QLabel { background-color: #1a1a1a; border: 2px solid #555; }");
    m_previewLabel->setText("Camera Preview\nClick 'Start Camera' to begin");
    m_previewLabel->setMinimumSize(640, 480);
    
    m_scrollArea->setWidget(m_previewLabel);
    m_scrollArea->setWidgetResizable(true);
    
    m_previewLayout->addLayout(m_previewControlsLayout);
    m_previewLayout->addLayout(m_zoomLayout);
    m_previewLayout->addWidget(m_scrollArea);
    
    // Camera controls panel
    m_cameraControls = new CameraControls();
    m_cameraControls->setCameraCapture(m_cameraCapture);
    m_cameraControls->setMaximumWidth(350);
    m_cameraControls->setMinimumWidth(300);
    
    // Add to splitter
    m_splitter->addWidget(m_previewWidget);
    m_splitter->addWidget(m_cameraControls);
    m_splitter->setStretchFactor(0, 1);
    m_splitter->setStretchFactor(1, 0);
    
    // Populate camera combo
    const auto cameras = m_cameraCapture->getAvailableCameras();
    for (int i = 0; i < cameras.size(); ++i) {
        m_cameraCombo->addItem(cameras[i].description(), i);
    }
}

void MainWindow::setupMenuBar()
{
    // File menu
    QMenu* fileMenu = menuBar()->addMenu("&File");
    
    m_startCameraAction = new QAction("&Start Camera", this);
    m_startCameraAction->setShortcut(QKeySequence("Ctrl+S"));
    fileMenu->addAction(m_startCameraAction);
    
    m_stopCameraAction = new QAction("S&top Camera", this);
    m_stopCameraAction->setShortcut(QKeySequence("Ctrl+T"));
    m_stopCameraAction->setEnabled(false);
    fileMenu->addAction(m_stopCameraAction);
    
    fileMenu->addSeparator();
    
    m_captureAction = new QAction("&Capture Photo", this);
    m_captureAction->setShortcut(QKeySequence("Space"));
    m_captureAction->setEnabled(false);
    fileMenu->addAction(m_captureAction);
    
    m_saveAction = new QAction("&Save Image", this);
    m_saveAction->setShortcut(QKeySequence("Ctrl+S"));
    m_saveAction->setEnabled(false);
    fileMenu->addAction(m_saveAction);
    
    m_loadAction = new QAction("&Load Image", this);
    m_loadAction->setShortcut(QKeySequence("Ctrl+O"));
    fileMenu->addAction(m_loadAction);
    
    fileMenu->addSeparator();
    
    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence("Ctrl+Q"));
    fileMenu->addAction(m_exitAction);
    
    // View menu
    QMenu* viewMenu = menuBar()->addMenu("&View");
    
    m_fullscreenAction = new QAction("&Fullscreen", this);
    m_fullscreenAction->setShortcut(QKeySequence("F11"));
    m_fullscreenAction->setCheckable(true);
    viewMenu->addAction(m_fullscreenAction);
    
    viewMenu->addSeparator();
    
    QAction* zoomInAction = new QAction("Zoom &In", this);
    zoomInAction->setShortcut(QKeySequence("Ctrl++"));
    viewMenu->addAction(zoomInAction);
    
    QAction* zoomOutAction = new QAction("Zoom &Out", this);
    zoomOutAction->setShortcut(QKeySequence("Ctrl+-"));
    viewMenu->addAction(zoomOutAction);
    
    QAction* zoomFitAction = new QAction("&Fit to Window", this);
    zoomFitAction->setShortcut(QKeySequence("Ctrl+0"));
    viewMenu->addAction(zoomFitAction);
    
    QAction* zoomActualAction = new QAction("&Actual Size", this);
    zoomActualAction->setShortcut(QKeySequence("Ctrl+1"));
    viewMenu->addAction(zoomActualAction);
    
    // Help menu
    QMenu* helpMenu = menuBar()->addMenu("&Help");
    
    m_aboutAction = new QAction("&About", this);
    helpMenu->addAction(m_aboutAction);
    
    // Connect menu actions
    connect(m_startCameraAction, &QAction::triggered, this, &MainWindow::onStartStopCamera);
    connect(m_stopCameraAction, &QAction::triggered, this, &MainWindow::onStartStopCamera);
    connect(m_captureAction, &QAction::triggered, this, &MainWindow::onCapturePhoto);
    connect(m_saveAction, &QAction::triggered, this, &MainWindow::onSaveImage);
    connect(m_loadAction, &QAction::triggered, this, &MainWindow::onLoadImage);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    connect(m_fullscreenAction, &QAction::triggered, this, &MainWindow::onToggleFullscreen);
    connect(zoomInAction, &QAction::triggered, this, &MainWindow::onZoomIn);
    connect(zoomOutAction, &QAction::triggered, this, &MainWindow::onZoomOut);
    connect(zoomFitAction, &QAction::triggered, this, &MainWindow::onZoomFit);
    connect(zoomActualAction, &QAction::triggered, this, &MainWindow::onZoomActual);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::onShowAbout);
}

void MainWindow::setupStatusBar()
{
    m_statusLabel = new QLabel("Ready");
    m_fpsLabel = new QLabel("FPS: 0");
    m_resolutionLabel = new QLabel("Resolution: N/A");
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);

    statusBar()->addWidget(m_statusLabel);
    statusBar()->addPermanentWidget(m_resolutionLabel);
    statusBar()->addPermanentWidget(m_fpsLabel);
    statusBar()->addPermanentWidget(m_progressBar);
}

void MainWindow::connectSignals()
{
    // Camera capture signals
    connect(m_cameraCapture, &CameraCapture::frameReady, this, &MainWindow::onFrameReady);
    connect(m_cameraCapture, &CameraCapture::processedFrameReady, this, &MainWindow::onProcessedFrameReady);
    connect(m_cameraCapture, &CameraCapture::photoCapture, this, &MainWindow::onPhotoCapture);
    connect(m_cameraCapture, &CameraCapture::errorOccurred, this, &MainWindow::onCameraError);
    connect(m_cameraCapture, &CameraCapture::cameraStatusChanged, this, &MainWindow::onCameraStatusChanged);

    // Camera controls signals
    connect(m_cameraControls, &CameraControls::cameraSettingsChanged, this, &MainWindow::onCameraSettingsChanged);
    connect(m_cameraControls, &CameraControls::enhancementSettingsChanged, this, &MainWindow::onEnhancementSettingsChanged);
    connect(m_cameraControls, &CameraControls::capturePhoto, this, &MainWindow::onCapturePhoto);

    // UI controls
    connect(m_startStopBtn, &QPushButton::clicked, this, &MainWindow::onStartStopCamera);
    connect(m_captureBtn, &QPushButton::clicked, this, &MainWindow::onCapturePhoto);
    connect(m_saveBtn, &QPushButton::clicked, this, &MainWindow::onSaveImage);
    connect(m_processingCheck, &QCheckBox::toggled, this, &MainWindow::onToggleProcessing);
    connect(m_cameraCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::onSelectCamera);

    // Zoom controls
    connect(m_zoomInBtn, &QPushButton::clicked, this, &MainWindow::onZoomIn);
    connect(m_zoomOutBtn, &QPushButton::clicked, this, &MainWindow::onZoomOut);
    connect(m_zoomFitBtn, &QPushButton::clicked, this, &MainWindow::onZoomFit);
    connect(m_zoomActualBtn, &QPushButton::clicked, this, &MainWindow::onZoomActual);
}

void MainWindow::onFrameReady(const cv::Mat& frame)
{
    if (!m_processingCheck->isChecked()) {
        updatePreview(frame, false);
    }
    m_currentFrame = frame.clone();
    m_frameCount++;
}

void MainWindow::onProcessedFrameReady(const cv::Mat& frame)
{
    if (m_processingCheck->isChecked()) {
        updatePreview(frame, true);
    }
    m_frameCount++;
}

void MainWindow::onPhotoCapture(const cv::Mat& image)
{
    m_lastCapturedImage = image.clone();
    m_saveBtn->setEnabled(true);
    updatePreview(image, m_processingCheck->isChecked());
    showInfo("Photo captured successfully!");
}

void MainWindow::onCameraError(const QString& error)
{
    showError(QString("Camera Error: %1").arg(error));
}

void MainWindow::onCameraStatusChanged(bool available)
{
    m_captureBtn->setEnabled(available);
    m_startCameraAction->setEnabled(!available);
    m_stopCameraAction->setEnabled(available);
    m_captureAction->setEnabled(available);

    if (available) {
        m_startStopBtn->setText("Stop Camera");
        m_startStopBtn->setStyleSheet("QPushButton { background-color: #f44336; font-weight: bold; padding: 8px 16px; }");
        m_statusLabel->setText("Camera active");
    } else {
        m_startStopBtn->setText("Start Camera");
        m_startStopBtn->setStyleSheet("QPushButton { background-color: #4CAF50; font-weight: bold; padding: 8px 16px; }");
        m_statusLabel->setText("Camera stopped");
    }

    updateStatusBar();
}

void MainWindow::onCameraSettingsChanged(const CameraSettings& settings)
{
    m_cameraCapture->setCameraSettings(settings);
    updateStatusBar();
}

void MainWindow::onEnhancementSettingsChanged(const ImageEnhancementSettings& settings)
{
    m_cameraCapture->setEnhancementSettings(settings);
}

void MainWindow::onStartStopCamera()
{
    if (m_cameraCapture->isCapturing()) {
        m_cameraCapture->stopCapture();
    } else {
        if (!m_cameraCapture->startCapture()) {
            showError("Failed to start camera");
        }
    }
}

void MainWindow::onCapturePhoto()
{
    if (m_cameraCapture->isCapturing()) {
        m_cameraCapture->capturePhoto();
    } else if (!m_currentFrame.empty()) {
        // Process current frame if camera is not active
        ImageEnhancementSettings settings = m_cameraCapture->getEnhancementSettings();
        cv::Mat processed = m_imageProcessor->processImage(m_currentFrame, settings);
        onPhotoCapture(processed);
    }
}

void MainWindow::onSaveImage()
{
    if (!m_lastCapturedImage.empty()) {
        saveImageToFile(m_lastCapturedImage);
    }
}

void MainWindow::onLoadImage()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Load Image", QStandardPaths::writableLocation(QStandardPaths::PicturesLocation),
        "Image Files (*.png *.jpg *.jpeg *.bmp *.tiff)");

    if (!fileName.isEmpty()) {
        cv::Mat image = cv::imread(fileName.toStdString());
        if (!image.empty()) {
            m_currentFrame = image.clone();
            updatePreview(image, false);
            m_statusLabel->setText(QString("Loaded: %1").arg(QFileInfo(fileName).fileName()));
        } else {
            showError("Failed to load image");
        }
    }
}

void MainWindow::onToggleProcessing()
{
    bool enabled = m_processingCheck->isChecked();
    m_cameraCapture->enableRealTimeProcessing(enabled);

    if (!m_currentFrame.empty()) {
        updatePreview(m_currentFrame, enabled);
    }
}

void MainWindow::onSelectCamera()
{
    int index = m_cameraCombo->currentData().toInt();
    m_cameraCapture->selectCamera(index);
}

void MainWindow::onShowAbout()
{
    QMessageBox::about(this, "About Professional Camera",
        "<h2>Professional Camera v1.0</h2>"
        "<p>Advanced camera application with professional image enhancement.</p>"
        "<p><b>Features:</b></p>"
        "<ul>"
        "<li>Real-time image processing</li>"
        "<li>Professional camera controls</li>"
        "<li>Advanced noise reduction</li>"
        "<li>Intelligent sharpening</li>"
        "<li>Color enhancement</li>"
        "<li>HDR processing</li>"
        "</ul>"
        "<p>Built with Qt6 and OpenCV for professional image quality.</p>");
}

void MainWindow::onToggleFullscreen()
{
    if (m_isFullscreen) {
        showNormal();
        m_isFullscreen = false;
    } else {
        showFullScreen();
        m_isFullscreen = true;
    }
    m_fullscreenAction->setChecked(m_isFullscreen);
}

void MainWindow::onZoomIn()
{
    m_zoomFactor = std::min(m_zoomFactor * ZOOM_STEP, MAX_ZOOM);
    scalePreview();
}

void MainWindow::onZoomOut()
{
    m_zoomFactor = std::max(m_zoomFactor / ZOOM_STEP, MIN_ZOOM);
    scalePreview();
}

void MainWindow::onZoomFit()
{
    if (!m_currentFrame.empty() && m_scrollArea) {
        QSize scrollSize = m_scrollArea->size();
        QSize imageSize(m_currentFrame.cols, m_currentFrame.rows);

        double scaleX = double(scrollSize.width()) / imageSize.width();
        double scaleY = double(scrollSize.height()) / imageSize.height();
        m_zoomFactor = std::min(scaleX, scaleY) * 0.95; // 95% to leave some margin

        scalePreview();
    }
}

void MainWindow::onZoomActual()
{
    m_zoomFactor = 1.0;
    scalePreview();
}

void MainWindow::updatePreview(const cv::Mat& frame, bool isProcessed)
{
    if (frame.empty()) {
        return;
    }

    QPixmap pixmap = matToQPixmap(frame);
    if (pixmap.isNull()) {
        return;
    }

    // Scale pixmap according to zoom factor
    if (m_zoomFactor != 1.0) {
        QSize newSize = pixmap.size() * m_zoomFactor;
        pixmap = pixmap.scaled(newSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }

    m_previewLabel->setPixmap(pixmap);
    m_previewLabel->resize(pixmap.size());

    // Update zoom label
    m_zoomLabel->setText(QString("%1%").arg(int(m_zoomFactor * 100)));

    // Update resolution label
    m_resolutionLabel->setText(QString("Resolution: %1x%2").arg(frame.cols).arg(frame.rows));
}

void MainWindow::scalePreview()
{
    if (!m_currentFrame.empty()) {
        updatePreview(m_currentFrame, m_processingCheck->isChecked());
    }
}

QPixmap MainWindow::matToQPixmap(const cv::Mat& mat)
{
    if (mat.empty()) {
        return QPixmap();
    }

    cv::Mat rgbMat;
    if (mat.channels() == 3) {
        cv::cvtColor(mat, rgbMat, cv::COLOR_BGR2RGB);
    } else if (mat.channels() == 1) {
        cv::cvtColor(mat, rgbMat, cv::COLOR_GRAY2RGB);
    } else {
        return QPixmap();
    }

    QImage qimg(rgbMat.data, rgbMat.cols, rgbMat.rows, rgbMat.step, QImage::Format_RGB888);
    return QPixmap::fromImage(qimg);
}

void MainWindow::updateStatusBar()
{
    if (m_cameraCapture->isCapturing()) {
        CameraSettings settings = m_cameraCapture->getCameraSettings();
        QString status = QString("Camera: %1 | ISO: %2 | Exposure: %3 EV")
                        .arg(m_cameraCapture->getCurrentCameraName())
                        .arg(settings.iso)
                        .arg(settings.exposure, 0, 'f', 1);
        m_statusLabel->setText(status);
    }
}

void MainWindow::saveImageToFile(const cv::Mat& image)
{
    if (image.empty()) {
        showError("No image to save");
        return;
    }

    QString defaultPath = QStandardPaths::writableLocation(QStandardPaths::PicturesLocation);
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd_hh-mm-ss");
    QString defaultName = QString("professional_camera_%1.png").arg(timestamp);
    QString defaultFile = QDir(defaultPath).filePath(defaultName);

    QString fileName = QFileDialog::getSaveFileName(this,
        "Save Image", defaultFile,
        "PNG Files (*.png);;JPEG Files (*.jpg);;TIFF Files (*.tiff);;BMP Files (*.bmp)");

    if (!fileName.isEmpty()) {
        if (cv::imwrite(fileName.toStdString(), image)) {
            showInfo(QString("Image saved: %1").arg(QFileInfo(fileName).fileName()));
        } else {
            showError("Failed to save image");
        }
    }
}

void MainWindow::showError(const QString& message)
{
    QMessageBox::critical(this, "Error", message);
    m_statusLabel->setText(QString("Error: %1").arg(message));
}

void MainWindow::showInfo(const QString& message)
{
    QMessageBox::information(this, "Information", message);
    m_statusLabel->setText(message);
}

#include "MainWindow.moc"
