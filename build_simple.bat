@echo off
echo Building Simple Professional Camera Application...

echo Compiling with MinGW g++...
g++ -std=c++17 -O2 -static-libgcc -static-libstdc++ -o camera_simple.exe simple_camera.cpp -lvfw32 -lwinmm -lgdi32 -luser32 -lkernel32

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable created: camera_simple.exe

if exist "camera_simple.exe" (
    echo File size:
    dir camera_simple.exe | findstr camera_simple.exe
    echo.
    echo You can now run: camera_simple.exe
) else (
    echo Error: Executable not found!
)

pause
