# Professional Camera Application

A professional camera application that enhances image quality using advanced image processing algorithms. Transform your regular camera hardware into a professional-grade imaging system.

## Features

### 🎥 Professional Camera Controls
- **Manual Exposure Control**: Precise exposure adjustment (-3.0 to **** EV)
- **ISO Settings**: Full range ISO control (100-6400)
- **White Balance**: Temperature adjustment (2000K-10000K)
- **Focus Control**: Manual focus with fine adjustment
- **Resolution Options**: 720p, 1080p, 1440p, 4K support
- **Frame Rate Control**: 15, 24, 30, 60 FPS options

### 🖼️ Advanced Image Enhancement
- **Intelligent Noise Reduction**: Advanced denoising algorithms
- **Professional Sharpening**: Unsharp mask with customizable parameters
- **Color Enhancement**: Saturation, vibrance, contrast, brightness
- **Tone Mapping**: Shadow/highlight recovery, gamma correction
- **Color Correction**: Temperature and tint adjustment
- **HDR Processing**: High dynamic range enhancement
- **Lens Correction**: Vignetting correction and distortion removal

### 🎨 Real-time Processing
- **Live Preview**: Real-time enhanced preview
- **Professional Presets**: Portrait, Landscape, Low Light, Professional modes
- **Instant Capture**: High-quality photo capture with processing
- **Zoom Controls**: Detailed image inspection with zoom functionality

### 💾 Professional Workflow
- **High-Quality Export**: Save processed images in multiple formats
- **Batch Processing**: Apply settings to multiple images
- **Professional Interface**: Dark theme optimized for photography
- **Keyboard Shortcuts**: Efficient workflow with hotkeys

## System Requirements

### Required Dependencies
- **Qt6**: GUI framework with Multimedia support
- **OpenCV**: Computer vision and image processing library
- **CMake**: Build system (version 3.16 or higher)
- **Visual Studio 2022**: C++ compiler (Windows)

### Hardware Requirements
- **Camera**: USB webcam or built-in camera
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 100MB for application, additional space for images
- **Display**: 1280x720 minimum resolution

## Installation

### Windows (Recommended)

1. **Install Dependencies**:
   ```bash
   # Install Qt6 from https://www.qt.io/download
   # Install OpenCV from https://opencv.org/releases/
   # Install Visual Studio 2022 Community
   ```

2. **Clone and Build**:
   ```bash
   git clone <repository-url>
   cd professional-camera
   build.bat
   ```

3. **Run Application**:
   ```bash
   camera.exe
   ```

### Manual Build

1. **Configure Environment**:
   ```bash
   # Set Qt6 path
   set Qt6_DIR=C:\Qt\6.5.0\msvc2022_64\lib\cmake\Qt6
   
   # Set OpenCV path
   set OpenCV_DIR=C:\opencv\build
   ```

2. **Build with CMake**:
   ```bash
   mkdir build
   cd build
   cmake .. -G "Visual Studio 17 2022" -A x64
   cmake --build . --config Release
   ```

## Usage Guide

### Getting Started

1. **Launch Application**: Run `camera.exe`
2. **Select Camera**: Choose your camera from the dropdown
3. **Start Preview**: Click "Start Camera" to begin live preview
4. **Adjust Settings**: Use the control panels to enhance image quality
5. **Capture Photos**: Click "Capture" to take professional-quality photos

### Professional Controls

#### Camera Tab
- **Exposure**: Adjust brightness and exposure compensation
- **ISO**: Control sensor sensitivity for different lighting
- **White Balance**: Correct color temperature for accurate colors
- **Focus**: Fine-tune focus for sharp images
- **Quality**: Set resolution and frame rate

#### Enhancement Tab
- **Noise Reduction**: Remove digital noise for cleaner images
- **Sharpening**: Enhance detail and edge definition
- **Color Enhancement**: Boost saturation, vibrance, and contrast
- **Tone Mapping**: Balance shadows and highlights
- **Advanced**: HDR processing and lens corrections

#### Presets Tab
- **Portrait**: Optimized for people photography
- **Landscape**: Enhanced for scenic photography
- **Low Light**: Specialized for dark environments
- **Professional**: Balanced settings for general use

### Keyboard Shortcuts

- **Space**: Capture photo
- **Ctrl+S**: Start/stop camera
- **Ctrl+O**: Load image
- **Ctrl+S**: Save image
- **F11**: Toggle fullscreen
- **Ctrl++**: Zoom in
- **Ctrl+-**: Zoom out
- **Ctrl+0**: Fit to window
- **Ctrl+1**: Actual size

## Technical Details

### Image Processing Pipeline

1. **Capture**: High-resolution frame acquisition
2. **Noise Reduction**: Non-local means denoising
3. **Lens Correction**: Vignetting and distortion correction
4. **Color Correction**: Temperature and tint adjustment
5. **Tone Mapping**: Shadow/highlight recovery
6. **Color Enhancement**: Saturation and vibrance boost
7. **HDR Processing**: Dynamic range enhancement
8. **Sharpening**: Unsharp mask application

### Algorithms Used

- **Non-local Means Denoising**: Advanced noise reduction
- **Unsharp Masking**: Professional sharpening technique
- **HSV Color Space**: Accurate color adjustments
- **Gamma Correction**: Tone curve optimization
- **Local Contrast Enhancement**: Detail preservation
- **Edge-Preserving Filters**: Quality enhancement

## Troubleshooting

### Common Issues

**Camera Not Detected**:
- Check camera connections
- Verify camera drivers are installed
- Try different USB ports
- Restart application

**Build Errors**:
- Verify Qt6 and OpenCV installation paths
- Check CMake version (3.16+ required)
- Ensure Visual Studio 2022 is installed
- Update graphics drivers

**Performance Issues**:
- Reduce resolution for better performance
- Disable real-time processing if needed
- Close other applications using camera
- Check available RAM

**Image Quality**:
- Adjust lighting conditions
- Use appropriate presets
- Fine-tune enhancement settings
- Ensure camera lens is clean

## Contributing

We welcome contributions to improve the Professional Camera application:

1. Fork the repository
2. Create a feature branch
3. Make your improvements
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **Qt Framework**: Cross-platform GUI development
- **OpenCV**: Computer vision and image processing
- **Professional Photography Community**: Inspiration and feedback

---

**Transform your camera hardware into a professional imaging system with advanced software enhancement!**
