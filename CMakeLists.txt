cmake_minimum_required(VERSION 3.16)
project(ProfessionalCamera)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia MultimediaWidgets)
find_package(OpenCV REQUIRED)

# Set up Qt6
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})

# Source files
set(SOURCES
    main.cpp
    CameraApp.cpp
    CameraCapture.cpp
    ImageProcessor.cpp
    MainWindow.cpp
    CameraControls.cpp
)

set(HEADERS
    CameraApp.h
    CameraCapture.h
    ImageProcessor.h
    MainWindow.h
    CameraControls.h
)

# Create executable
add_executable(camera ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(camera 
    Qt6::Core 
    Qt6::Widgets 
    Qt6::Multimedia 
    Qt6::MultimediaWidgets
    ${OpenCV_LIBS}
)

# Set output name
set_target_properties(camera PROPERTIES OUTPUT_NAME "camera")

# Windows specific settings
if(WIN32)
    set_target_properties(camera PROPERTIES
        WIN32_EXECUTABLE TRUE
        OUTPUT_NAME "camera"
    )
endif()
