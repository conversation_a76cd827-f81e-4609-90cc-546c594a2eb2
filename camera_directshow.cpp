#include <iostream>
#include <windows.h>
#include <dshow.h>
#include <string>
#include <ctime>
#include <vector>
#include <comdef.h>

#pragma comment(lib, "strmiids.lib")
#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "oleaut32.lib")
#pragma comment(lib, "uuid.lib")

// Use ANSI versions for compatibility
#undef UNICODE
#undef _UNICODE

class DirectShowCameraApp {
private:
    HWND hwndMain;
    HWND hwndVideo;
    bool isCapturing;
    
    // DirectShow interfaces
    IGraphBuilder* pGraph;
    ICaptureGraphBuilder2* pCapture;
    IMediaControl* pControl;
    IVideoWindow* pVideoWindow;
    IBaseFilter* pCameraFilter;
    ISampleGrabber* pSampleGrabber;
    IBaseFilter* pGrabberFilter;
    
public:
    DirectShowCameraApp() : hwndMain(nullptr), hwndVideo(nullptr), isCapturing(false),
                           pGraph(nullptr), pCapture(nullptr), pControl(nullptr), 
                           pVideoWindow(nullptr), pCameraFilter(nullptr),
                           pSampleGrabber(nullptr), pGrabberFilter(nullptr) {}
    
    bool initialize() {
        // Initialize COM
        HRESULT hr = CoInitialize(nullptr);
        if (FAILED(hr)) {
            std::cout << "Failed to initialize COM" << std::endl;
            return false;
        }
        
        // Register window class
        WNDCLASS wc = {};
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = GetModuleHandle(nullptr);
        wc.lpszClassName = "DirectShowCameraApp";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        
        if (!RegisterClass(&wc)) {
            std::cout << "Failed to register window class" << std::endl;
            return false;
        }
        
        // Create main window
        hwndMain = CreateWindowEx(
            0,
            "DirectShowCameraApp",
            "Professional Camera - DirectShow Version",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT, 900, 700,
            nullptr, nullptr, GetModuleHandle(nullptr), this
        );
        
        if (!hwndMain) {
            std::cout << "Failed to create main window" << std::endl;
            return false;
        }
        
        // Create video window
        hwndVideo = CreateWindowEx(
            0, "STATIC", "",
            WS_CHILD | WS_VISIBLE | WS_BORDER,
            10, 80, 720, 540,
            hwndMain, nullptr, GetModuleHandle(nullptr), nullptr
        );
        
        // Create control buttons
        CreateWindow("BUTTON", "Start Camera",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
            750, 80, 120, 35, hwndMain, (HMENU)2, GetModuleHandle(nullptr), nullptr);
            
        CreateWindow("BUTTON", "Stop Camera",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            750, 125, 120, 35, hwndMain, (HMENU)3, GetModuleHandle(nullptr), nullptr);
            
        CreateWindow("BUTTON", "Capture Photo",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            750, 170, 120, 35, hwndMain, (HMENU)4, GetModuleHandle(nullptr), nullptr);
            
        CreateWindow("BUTTON", "List Cameras",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            750, 215, 120, 35, hwndMain, (HMENU)5, GetModuleHandle(nullptr), nullptr);
        
        // Add status text
        CreateWindow("STATIC", "Professional Camera v2.0\nDirectShow Edition\n\nThis version uses DirectShow\nfor better compatibility with\nmodern cameras.\n\nIf you see the camera light on\nbut black screen, this should\nfix the issue.",
            WS_VISIBLE | WS_CHILD,
            750, 270, 140, 300, hwndMain, nullptr, GetModuleHandle(nullptr), nullptr);
        
        ShowWindow(hwndMain, SW_SHOW);
        UpdateWindow(hwndMain);
        
        return true;
    }
    
    bool initializeDirectShow() {
        HRESULT hr;
        
        // Create the Filter Graph Manager
        hr = CoCreateInstance(CLSID_FilterGraph, nullptr, CLSCTX_INPROC_SERVER,
                             IID_IGraphBuilder, (void**)&pGraph);
        if (FAILED(hr)) {
            std::cout << "Failed to create Filter Graph Manager" << std::endl;
            return false;
        }
        
        // Create the Capture Graph Builder
        hr = CoCreateInstance(CLSID_CaptureGraphBuilder2, nullptr, CLSCTX_INPROC_SERVER,
                             IID_ICaptureGraphBuilder2, (void**)&pCapture);
        if (FAILED(hr)) {
            std::cout << "Failed to create Capture Graph Builder" << std::endl;
            return false;
        }
        
        // Set the filter graph
        hr = pCapture->SetFiltergraph(pGraph);
        if (FAILED(hr)) {
            std::cout << "Failed to set filter graph" << std::endl;
            return false;
        }
        
        // Get Media Control interface
        hr = pGraph->QueryInterface(IID_IMediaControl, (void**)&pControl);
        if (FAILED(hr)) {
            std::cout << "Failed to get Media Control interface" << std::endl;
            return false;
        }
        
        return true;
    }
    
    bool findCamera() {
        HRESULT hr;
        ICreateDevEnum* pDevEnum = nullptr;
        IEnumMoniker* pEnum = nullptr;
        
        // Create the System Device Enumerator
        hr = CoCreateInstance(CLSID_SystemDeviceEnum, nullptr, CLSCTX_INPROC_SERVER,
                             IID_ICreateDevEnum, (void**)&pDevEnum);
        if (FAILED(hr)) {
            std::cout << "Failed to create System Device Enumerator" << std::endl;
            return false;
        }
        
        // Create an enumerator for video capture devices
        hr = pDevEnum->CreateClassEnumerator(CLSID_VideoInputDeviceCategory, &pEnum, 0);
        if (hr == S_FALSE) {
            std::cout << "No video capture devices found" << std::endl;
            pDevEnum->Release();
            return false;
        }
        
        IMoniker* pMoniker = nullptr;
        while (pEnum->Next(1, &pMoniker, nullptr) == S_OK) {
            IPropertyBag* pPropBag;
            hr = pMoniker->BindToStorage(0, 0, IID_IPropertyBag, (void**)&pPropBag);
            if (SUCCEEDED(hr)) {
                VARIANT var;
                VariantInit(&var);
                
                // Get the friendly name
                hr = pPropBag->Read(L"FriendlyName", &var, 0);
                if (SUCCEEDED(hr)) {
                    std::wcout << L"Found camera: " << var.bstrVal << std::endl;
                    
                    // Bind to the filter
                    hr = pMoniker->BindToObject(0, 0, IID_IBaseFilter, (void**)&pCameraFilter);
                    if (SUCCEEDED(hr)) {
                        std::cout << "Successfully bound to camera filter" << std::endl;
                        VariantClear(&var);
                        pPropBag->Release();
                        pMoniker->Release();
                        break;
                    }
                }
                VariantClear(&var);
                pPropBag->Release();
            }
            pMoniker->Release();
        }
        
        pEnum->Release();
        pDevEnum->Release();
        
        return (pCameraFilter != nullptr);
    }
    
    void startCapture() {
        if (isCapturing) return;
        
        std::cout << "Starting DirectShow camera capture..." << std::endl;
        
        if (!initializeDirectShow()) {
            MessageBox(hwndMain, "Failed to initialize DirectShow", "Error", MB_OK | MB_ICONERROR);
            return;
        }
        
        if (!findCamera()) {
            MessageBox(hwndMain, "No camera found or failed to connect", "Error", MB_OK | MB_ICONERROR);
            return;
        }
        
        HRESULT hr;
        
        // Add the camera filter to the graph
        hr = pGraph->AddFilter(pCameraFilter, L"Camera");
        if (FAILED(hr)) {
            std::cout << "Failed to add camera filter to graph" << std::endl;
            MessageBox(hwndMain, "Failed to add camera to graph", "Error", MB_OK | MB_ICONERROR);
            return;
        }
        
        // Get the video window interface
        hr = pGraph->QueryInterface(IID_IVideoWindow, (void**)&pVideoWindow);
        if (SUCCEEDED(hr)) {
            // Set the video window
            pVideoWindow->put_Owner((OAHWND)hwndVideo);
            pVideoWindow->put_WindowStyle(WS_CHILD | WS_CLIPCHILDREN);
            
            RECT rect;
            GetClientRect(hwndVideo, &rect);
            pVideoWindow->SetWindowPosition(0, 0, rect.right, rect.bottom);
        }
        
        // Render the preview stream
        hr = pCapture->RenderStream(&PIN_CATEGORY_PREVIEW, &MEDIATYPE_Video,
                                   pCameraFilter, nullptr, nullptr);
        if (FAILED(hr)) {
            std::cout << "Failed to render preview stream" << std::endl;
            MessageBox(hwndMain, "Failed to render camera preview", "Error", MB_OK | MB_ICONERROR);
            return;
        }
        
        // Start the graph
        hr = pControl->Run();
        if (FAILED(hr)) {
            std::cout << "Failed to start the graph" << std::endl;
            MessageBox(hwndMain, "Failed to start camera", "Error", MB_OK | MB_ICONERROR);
            return;
        }
        
        isCapturing = true;
        std::cout << "Camera started successfully!" << std::endl;
        MessageBox(hwndMain, "Camera started! You should now see live video.", "Success", MB_OK | MB_ICONINFORMATION);
    }
    
    void stopCapture() {
        if (!isCapturing) return;
        
        if (pControl) {
            pControl->Stop();
        }
        
        if (pVideoWindow) {
            pVideoWindow->put_Visible(OAFALSE);
            pVideoWindow->put_Owner(NULL);
        }
        
        isCapturing = false;
        std::cout << "Camera stopped" << std::endl;
    }
    
    void capturePhoto() {
        if (!isCapturing) {
            MessageBox(hwndMain, "Please start the camera first", "Camera Not Active", MB_OK | MB_ICONWARNING);
            return;
        }
        
        // For now, just show a message - photo capture requires additional implementation
        MessageBox(hwndMain, "Photo capture feature coming soon!\nFor now, you can use Windows Snipping Tool\nto capture the live video preview.", "Info", MB_OK | MB_ICONINFORMATION);
    }
    
    void listCameras() {
        HRESULT hr;
        ICreateDevEnum* pDevEnum = nullptr;
        IEnumMoniker* pEnum = nullptr;
        
        hr = CoCreateInstance(CLSID_SystemDeviceEnum, nullptr, CLSCTX_INPROC_SERVER,
                             IID_ICreateDevEnum, (void**)&pDevEnum);
        if (FAILED(hr)) return;
        
        hr = pDevEnum->CreateClassEnumerator(CLSID_VideoInputDeviceCategory, &pEnum, 0);
        if (hr == S_FALSE) {
            MessageBox(hwndMain, "No video capture devices found", "No Cameras", MB_OK | MB_ICONWARNING);
            pDevEnum->Release();
            return;
        }
        
        std::string cameraList = "Available Cameras:\n\n";
        int count = 0;
        
        IMoniker* pMoniker = nullptr;
        while (pEnum->Next(1, &pMoniker, nullptr) == S_OK) {
            IPropertyBag* pPropBag;
            hr = pMoniker->BindToStorage(0, 0, IID_IPropertyBag, (void**)&pPropBag);
            if (SUCCEEDED(hr)) {
                VARIANT var;
                VariantInit(&var);
                
                hr = pPropBag->Read(L"FriendlyName", &var, 0);
                if (SUCCEEDED(hr)) {
                    // Convert wide string to narrow string
                    int len = WideCharToMultiByte(CP_UTF8, 0, var.bstrVal, -1, nullptr, 0, nullptr, nullptr);
                    char* buffer = new char[len];
                    WideCharToMultiByte(CP_UTF8, 0, var.bstrVal, -1, buffer, len, nullptr, nullptr);
                    
                    cameraList += std::to_string(count) + ": " + buffer + "\n";
                    delete[] buffer;
                    count++;
                }
                VariantClear(&var);
                pPropBag->Release();
            }
            pMoniker->Release();
        }
        
        if (count == 0) {
            cameraList = "No cameras detected";
        }
        
        MessageBox(hwndMain, cameraList.c_str(), "Camera List", MB_OK | MB_ICONINFORMATION);
        
        pEnum->Release();
        pDevEnum->Release();
    }
    
    void cleanup() {
        stopCapture();
        
        if (pVideoWindow) pVideoWindow->Release();
        if (pControl) pControl->Release();
        if (pCameraFilter) pCameraFilter->Release();
        if (pCapture) pCapture->Release();
        if (pGraph) pGraph->Release();
        
        CoUninitialize();
    }
    
    void run() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        DirectShowCameraApp* app = nullptr;
        
        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* pCreate = reinterpret_cast<CREATESTRUCT*>(lParam);
            app = reinterpret_cast<DirectShowCameraApp*>(pCreate->lpCreateParams);
            SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(app));
        } else {
            app = reinterpret_cast<DirectShowCameraApp*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
        }
        
        if (app) {
            switch (uMsg) {
                case WM_COMMAND:
                    switch (LOWORD(wParam)) {
                        case 2: // Start Camera
                            app->startCapture();
                            break;
                        case 3: // Stop Camera
                            app->stopCapture();
                            break;
                        case 4: // Capture Photo
                            app->capturePhoto();
                            break;
                        case 5: // List Cameras
                            app->listCameras();
                            break;
                    }
                    break;
                    
                case WM_DESTROY:
                    app->cleanup();
                    PostQuitMessage(0);
                    break;
            }
        }
        
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
};

int main() {
    std::cout << "Professional Camera Application - DirectShow Edition" << std::endl;
    std::cout << "This version should fix the black screen issue!" << std::endl;
    
    DirectShowCameraApp app;
    
    if (!app.initialize()) {
        std::cout << "Failed to initialize camera application" << std::endl;
        MessageBox(nullptr, "Failed to initialize camera application.", "Initialization Error", MB_OK | MB_ICONERROR);
        return -1;
    }
    
    std::cout << "Camera application started successfully!" << std::endl;
    std::cout << "Click 'Start Camera' to begin preview with DirectShow" << std::endl;
    
    app.run();
    
    return 0;
}
