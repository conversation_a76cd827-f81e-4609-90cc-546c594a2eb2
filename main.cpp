#include "CameraApp.h"
#include <QDebug>
#include <QDir>
#include <QStandardPaths>

int main(int argc, char *argv[])
{
    // Enable high DPI scaling
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    CameraApp app(argc, argv);
    
    // Initialize the application
    if (!app.initialize()) {
        qCritical() << "Failed to initialize Professional Camera application";
        return -1;
    }
    
    qDebug() << "Professional Camera application started successfully";
    qDebug() << "Application directory:" << QDir::currentPath();
    qDebug() << "Pictures directory:" << QStandardPaths::writableLocation(QStandardPaths::PicturesLocation);
    
    return app.exec();
}
