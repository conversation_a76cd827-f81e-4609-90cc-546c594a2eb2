// Simple test to verify the basic structure compiles
#include <iostream>
#include <opencv2/opencv.hpp>

int main() {
    std::cout << "Testing OpenCV..." << std::endl;
    
    // Test OpenCV basic functionality
    cv::Mat testImage = cv::Mat::zeros(100, 100, CV_8UC3);
    if (!testImage.empty()) {
        std::cout << "OpenCV test passed!" << std::endl;
        std::cout << "Image size: " << testImage.cols << "x" << testImage.rows << std::endl;
    } else {
        std::cout << "OpenCV test failed!" << std::endl;
        return 1;
    }
    
    std::cout << "OpenCV version: " << CV_VERSION << std::endl;
    std::cout << "Basic structure test completed successfully!" << std::endl;
    
    return 0;
}
